import 'package:flutter/material.dart';

class AppProvider with ChangeNotifier {
  // إعدادات التطبيق
  bool _isDarkMode = false;
  double _fontSize = 16.0;
  String _selectedLanguage = 'ar';
  bool _isFirstLaunch = true;

  // حالة التطبيق العامة
  bool _isLoading = false;
  String _errorMessage = '';
  int _currentTabIndex = 0;

  // إحصائيات التطبيق
  int _totalSeerahEvents = 0;
  int _totalHadiths = 0;
  int _completedEvents = 0;
  int _favoriteHadiths = 0;

  // Getters
  bool get isDarkMode => _isDarkMode;
  double get fontSize => _fontSize;
  String get selectedLanguage => _selectedLanguage;
  bool get isFirstLaunch => _isFirstLaunch;
  bool get isLoading => _isLoading;
  String get errorMessage => _errorMessage;
  int get currentTabIndex => _currentTabIndex;
  int get totalSeerahEvents => _totalSeerahEvents;
  int get totalHadiths => _totalHadiths;
  int get completedEvents => _completedEvents;
  int get favoriteHadiths => _favoriteHadiths;

  // الحصول على نسبة التقدم
  double get progressPercentage {
    if (_totalSeerahEvents == 0) return 0.0;
    return _completedEvents / _totalSeerahEvents;
  }

  // تبديل الوضع المظلم
  void toggleDarkMode() {
    _isDarkMode = !_isDarkMode;
    notifyListeners();
    _saveSettings();
  }

  // تغيير حجم الخط
  void setFontSize(double size) {
    if (size >= 12.0 && size <= 24.0) {
      _fontSize = size;
      notifyListeners();
      _saveSettings();
    }
  }

  // تغيير اللغة
  void setLanguage(String language) {
    if (_selectedLanguage != language) {
      _selectedLanguage = language;
      notifyListeners();
      _saveSettings();
    }
  }

  // تعيين حالة التحميل
  void setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  // تعيين رسالة خطأ
  void setError(String error) {
    _errorMessage = error;
    notifyListeners();

    // مسح الخطأ بعد 5 ثوان
    if (error.isNotEmpty) {
      Future.delayed(const Duration(seconds: 5), () {
        if (_errorMessage == error) {
          _errorMessage = '';
          notifyListeners();
        }
      });
    }
  }

  // تغيير التبويب الحالي
  void setCurrentTab(int index) {
    if (_currentTabIndex != index && index >= 0 && index <= 2) {
      _currentTabIndex = index;
      notifyListeners();
    }
  }

  // تحديث الإحصائيات
  void updateStatistics({
    int? seerahEvents,
    int? hadiths,
    int? completed,
    int? favorites,
  }) {
    bool hasChanges = false;

    if (seerahEvents != null && _totalSeerahEvents != seerahEvents) {
      _totalSeerahEvents = seerahEvents;
      hasChanges = true;
    }

    if (hadiths != null && _totalHadiths != hadiths) {
      _totalHadiths = hadiths;
      hasChanges = true;
    }

    if (completed != null && _completedEvents != completed) {
      _completedEvents = completed;
      hasChanges = true;
    }

    if (favorites != null && _favoriteHadiths != favorites) {
      _favoriteHadiths = favorites;
      hasChanges = true;
    }

    if (hasChanges) {
      notifyListeners();
    }
  }

  // إنهاء الإعداد الأولي
  void completeFirstLaunch() {
    _isFirstLaunch = false;
    notifyListeners();
    _saveSettings();
  }

  // تحميل الإعدادات
  Future<void> loadSettings() async {
    try {
      // محاكاة تحميل الإعدادات من التخزين المحلي
      await Future.delayed(const Duration(milliseconds: 300));

      // هنا يمكن إضافة تحميل الإعدادات من SharedPreferences
      // _isDarkMode = prefs.getBool('isDarkMode') ?? false;
      // _fontSize = prefs.getDouble('fontSize') ?? 16.0;
      // إلخ...

      notifyListeners();
    } catch (e) {
      setError('خطأ في تحميل الإعدادات: $e');
    }
  }

  // حفظ الإعدادات
  Future<void> _saveSettings() async {
    try {
      // محاكاة حفظ الإعدادات في التخزين المحلي
      await Future.delayed(const Duration(milliseconds: 100));

      // هنا يمكن إضافة حفظ الإعدادات في SharedPreferences
      // await prefs.setBool('isDarkMode', _isDarkMode);
      // await prefs.setDouble('fontSize', _fontSize);
      // إلخ...

    } catch (e) {
      setError('خطأ في حفظ الإعدادات: $e');
    }
  }

  // إعادة تعيين التطبيق
  void resetApp() {
    _isDarkMode = false;
    _fontSize = 16.0;
    _selectedLanguage = 'ar';
    _isFirstLaunch = true;
    _currentTabIndex = 0;
    _totalSeerahEvents = 0;
    _totalHadiths = 0;
    _completedEvents = 0;
    _favoriteHadiths = 0;
    _errorMessage = '';
    notifyListeners();
    _saveSettings();
  }

  // الحصول على موضوع التطبيق
  ThemeData getTheme() {
    return ThemeData(
      brightness: _isDarkMode ? Brightness.dark : Brightness.light,
      primarySwatch: Colors.blue,
      fontFamily: 'Arial',
      textTheme: TextTheme(
        bodyLarge: TextStyle(fontSize: _fontSize),
        bodyMedium: TextStyle(fontSize: _fontSize - 2),
        titleLarge: TextStyle(fontSize: _fontSize + 4),
      ),
    );
  }
}
