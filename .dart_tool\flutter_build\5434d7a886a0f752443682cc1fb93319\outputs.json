["D:\\uuu\\test1\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "D:\\uuu\\test1\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "D:\\uuu\\test1\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "D:\\uuu\\test1\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "D:\\uuu\\test1\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "D:\\uuu\\test1\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "D:\\uuu\\test1\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "D:\\uuu\\test1\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "D:\\uuu\\test1\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "D:\\uuu\\test1\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "D:\\uuu\\test1\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]