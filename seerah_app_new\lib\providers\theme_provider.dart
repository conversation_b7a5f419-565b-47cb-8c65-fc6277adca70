import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum ThemeMode { light, dark, system }

class ThemeProvider with ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;
  double _fontSize = 1.0;
  bool _isHighContrast = false;
  
  // Getters
  ThemeMode get themeMode => _themeMode;
  double get fontSize => _fontSize;
  bool get isHighContrast => _isHighContrast;
  
  bool get isDarkMode {
    if (_themeMode == ThemeMode.system) {
      return WidgetsBinding.instance.platformDispatcher.platformBrightness == Brightness.dark;
    }
    return _themeMode == ThemeMode.dark;
  }
  
  // تحميل الإعدادات من التخزين المحلي
  Future<void> loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // تحميل وضع الثيم
      final themeModeIndex = prefs.getInt('theme_mode') ?? 2; // system by default
      _themeMode = ThemeMode.values[themeModeIndex];
      
      // تحميل حجم الخط
      _fontSize = prefs.getDouble('font_size') ?? 1.0;
      
      // تحميل وضع التباين العالي
      _isHighContrast = prefs.getBool('high_contrast') ?? false;
      
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحميل إعدادات الثيم: $e');
    }
  }
  
  // حفظ الإعدادات في التخزين المحلي
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setInt('theme_mode', _themeMode.index);
      await prefs.setDouble('font_size', _fontSize);
      await prefs.setBool('high_contrast', _isHighContrast);
    } catch (e) {
      debugPrint('خطأ في حفظ إعدادات الثيم: $e');
    }
  }
  
  // تغيير وضع الثيم
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode != mode) {
      _themeMode = mode;
      notifyListeners();
      await _saveSettings();
    }
  }
  
  // تبديل بين الوضع الفاتح والمظلم
  Future<void> toggleTheme() async {
    if (_themeMode == ThemeMode.light) {
      await setThemeMode(ThemeMode.dark);
    } else if (_themeMode == ThemeMode.dark) {
      await setThemeMode(ThemeMode.system);
    } else {
      await setThemeMode(ThemeMode.light);
    }
  }
  
  // تغيير حجم الخط
  Future<void> setFontSize(double size) async {
    if (_fontSize != size && size >= 0.8 && size <= 1.5) {
      _fontSize = size;
      notifyListeners();
      await _saveSettings();
    }
  }
  
  // زيادة حجم الخط
  Future<void> increaseFontSize() async {
    final newSize = (_fontSize + 0.1).clamp(0.8, 1.5);
    await setFontSize(newSize);
  }
  
  // تقليل حجم الخط
  Future<void> decreaseFontSize() async {
    final newSize = (_fontSize - 0.1).clamp(0.8, 1.5);
    await setFontSize(newSize);
  }
  
  // إعادة تعيين حجم الخط
  Future<void> resetFontSize() async {
    await setFontSize(1.0);
  }
  
  // تبديل وضع التباين العالي
  Future<void> toggleHighContrast() async {
    _isHighContrast = !_isHighContrast;
    notifyListeners();
    await _saveSettings();
  }
  
  // إعادة تعيين جميع الإعدادات
  Future<void> resetSettings() async {
    _themeMode = ThemeMode.system;
    _fontSize = 1.0;
    _isHighContrast = false;
    notifyListeners();
    await _saveSettings();
  }
  
  // الحصول على اسم الوضع الحالي
  String get currentThemeName {
    switch (_themeMode) {
      case ThemeMode.light:
        return 'الوضع النهاري';
      case ThemeMode.dark:
        return 'الوضع الليلي';
      case ThemeMode.system:
        return 'وضع النظام';
    }
  }
  
  // الحصول على أيقونة الوضع الحالي
  IconData get currentThemeIcon {
    switch (_themeMode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.brightness_auto;
    }
  }
  
  // الحصول على وصف حجم الخط
  String get fontSizeDescription {
    if (_fontSize <= 0.9) {
      return 'صغير';
    } else if (_fontSize <= 1.1) {
      return 'عادي';
    } else if (_fontSize <= 1.3) {
      return 'كبير';
    } else {
      return 'كبير جداً';
    }
  }
  
  // تطبيق حجم الخط على TextStyle
  TextStyle applyFontSize(TextStyle style) {
    return style.copyWith(
      fontSize: (style.fontSize ?? 14.0) * _fontSize,
    );
  }
  
  // الحصول على ألوان التباين العالي
  ColorScheme getHighContrastColors(ColorScheme original) {
    if (!_isHighContrast) return original;
    
    return original.copyWith(
      primary: Colors.black,
      onPrimary: Colors.white,
      secondary: Colors.black,
      onSecondary: Colors.white,
      surface: Colors.white,
      onSurface: Colors.black,
      background: Colors.white,
      onBackground: Colors.black,
    );
  }
  
  // معلومات الإعدادات للعرض
  Map<String, dynamic> get settingsInfo => {
    'theme_mode': currentThemeName,
    'font_size': fontSizeDescription,
    'font_size_value': _fontSize,
    'high_contrast': _isHighContrast,
    'is_dark_mode': isDarkMode,
  };
}
