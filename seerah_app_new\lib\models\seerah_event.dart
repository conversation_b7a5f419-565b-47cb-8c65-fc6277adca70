class SeerahEvent {
  final String id;
  final String title;
  final String subtitle;
  final String description;
  final String date;
  final String location;
  final String category;
  final String iconName;
  final bool isAvailable;
  final int order;

  const SeerahEvent({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.description,
    required this.date,
    required this.location,
    required this.category,
    required this.iconName,
    this.isAvailable = true,
    required this.order,
  });

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'subtitle': subtitle,
      'description': description,
      'date': date,
      'location': location,
      'category': category,
      'iconName': iconName,
      'isAvailable': isAvailable,
      'order': order,
    };
  }

  // إنشاء من JSON
  factory SeerahEvent.fromJson(Map<String, dynamic> json) {
    return SeerahEvent(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      subtitle: json['subtitle'] ?? '',
      description: json['description'] ?? '',
      date: json['date'] ?? '',
      location: json['location'] ?? '',
      category: json['category'] ?? '',
      iconName: json['iconName'] ?? '',
      isAvailable: json['isAvailable'] ?? true,
      order: json['order'] ?? 0,
    );
  }

  // نسخ مع تعديل
  SeerahEvent copyWith({
    String? id,
    String? title,
    String? subtitle,
    String? description,
    String? date,
    String? location,
    String? category,
    String? iconName,
    bool? isAvailable,
    int? order,
  }) {
    return SeerahEvent(
      id: id ?? this.id,
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      description: description ?? this.description,
      date: date ?? this.date,
      location: location ?? this.location,
      category: category ?? this.category,
      iconName: iconName ?? this.iconName,
      isAvailable: isAvailable ?? this.isAvailable,
      order: order ?? this.order,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SeerahEvent && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SeerahEvent(id: $id, title: $title, order: $order)';
  }
}
