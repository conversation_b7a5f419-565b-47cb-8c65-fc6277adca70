import 'package:flutter/foundation.dart';
import '../models/seerah_event.dart';

class SeerahProvider with ChangeNotifier {
  List<SeerahEvent> _events = [];
  List<SeerahEvent> _filteredEvents = [];
  String _selectedCategory = 'الكل';
  bool _isLoading = false;
  String _searchQuery = '';

  // Getters
  List<SeerahEvent> get events => _filteredEvents;
  List<SeerahEvent> get allEvents => _events;
  String get selectedCategory => _selectedCategory;
  bool get isLoading => _isLoading;
  String get searchQuery => _searchQuery;

  // الحصول على الفئات المتاحة
  List<String> get categories {
    final cats = _events.map((e) => e.category).toSet().toList();
    cats.insert(0, 'الكل');
    return cats;
  }

  // الحصول على عدد الأحداث في كل فئة
  Map<String, int> get categoryCounts {
    final counts = <String, int>{};
    for (final event in _events) {
      counts[event.category] = (counts[event.category] ?? 0) + 1;
    }
    return counts;
  }

  // تحميل البيانات الأولية
  Future<void> loadInitialData() async {
    _isLoading = true;
    notifyListeners();

    try {
      // محاكاة تحميل البيانات
      await Future.delayed(const Duration(milliseconds: 500));

      _events = _getSampleEvents();
      _applyFilters();

    } catch (e) {
      debugPrint('خطأ في تحميل بيانات السيرة: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // تطبيق الفلاتر
  void _applyFilters() {
    _filteredEvents = _events.where((event) {
      final matchesCategory = _selectedCategory == 'الكل' ||
                             event.category == _selectedCategory;
      final matchesSearch = _searchQuery.isEmpty ||
                           event.title.contains(_searchQuery) ||
                           event.subtitle.contains(_searchQuery) ||
                           event.description.contains(_searchQuery);

      return matchesCategory && matchesSearch && event.isAvailable;
    }).toList();

    // ترتيب حسب الترتيب المحدد
    _filteredEvents.sort((a, b) => a.order.compareTo(b.order));
  }

  // تغيير الفئة المختارة
  void setCategory(String category) {
    if (_selectedCategory != category) {
      _selectedCategory = category;
      _applyFilters();
      notifyListeners();
    }
  }

  // تحديث البحث
  void updateSearch(String query) {
    if (_searchQuery != query) {
      _searchQuery = query;
      _applyFilters();
      notifyListeners();
    }
  }

  // الحصول على حدث بالمعرف
  SeerahEvent? getEventById(String id) {
    try {
      return _events.firstWhere((event) => event.id == id);
    } catch (e) {
      return null;
    }
  }

  // إضافة حدث جديد
  void addEvent(SeerahEvent event) {
    _events.add(event);
    _applyFilters();
    notifyListeners();
  }

  // تحديث حدث
  void updateEvent(SeerahEvent updatedEvent) {
    final index = _events.indexWhere((event) => event.id == updatedEvent.id);
    if (index != -1) {
      _events[index] = updatedEvent;
      _applyFilters();
      notifyListeners();
    }
  }

  // حذف حدث
  void removeEvent(String id) {
    _events.removeWhere((event) => event.id == id);
    _applyFilters();
    notifyListeners();
  }

  // البحث في الأحداث
  List<SeerahEvent> searchEvents(String query) {
    if (query.isEmpty) {
      return [];
    }

    return _events.where((event) {
      return event.isAvailable && (
        event.title.contains(query) ||
        event.subtitle.contains(query) ||
        event.description.contains(query) ||
        event.category.contains(query) ||
        event.location.contains(query) ||
        event.date.contains(query)
      );
    }).toList()..sort((a, b) => a.order.compareTo(b.order));
  }

  // بيانات شاملة للسيرة النبوية
  List<SeerahEvent> _getSampleEvents() {
    return [
      // الطفولة والشباب
      const SeerahEvent(
        id: '1',
        title: 'وفاة والده عبد الله',
        subtitle: 'قبل ولادته ﷺ',
        description: 'توفي والد النبي ﷺ عبد الله بن عبد المطلب قبل ولادته، فنشأ يتيم الأب. كان عبد الله تاجراً شاباً توفي في رحلة تجارية إلى الشام.',
        date: '570 م',
        location: 'المدينة المنورة',
        category: 'الطفولة والشباب',
        iconName: 'heart_broken',
        order: 1,
      ),
      const SeerahEvent(
        id: '2',
        title: 'مولد النبي ﷺ',
        subtitle: 'عام الفيل - مكة المكرمة',
        description: 'وُلد النبي محمد ﷺ في مكة المكرمة في عام الفيل، وهو العام الذي حاول فيه أبرهة الحبشي هدم الكعبة المشرفة. وُلد في شهر ربيع الأول يوم الاثنين.',
        date: '571 م - 12 ربيع الأول',
        location: 'مكة المكرمة',
        category: 'الطفولة والشباب',
        iconName: 'star',
        order: 2,
      ),
      const SeerahEvent(
        id: '3',
        title: 'الرضاعة عند حليمة السعدية',
        subtitle: 'في بادية بني سعد',
        description: 'أرضعته حليمة السعدية في بادية بني سعد، حيث نشأ في بيئة صحراوية نقية. شهدت حليمة بركات عظيمة في بيتها وماشيتها بسبب وجود النبي ﷺ.',
        date: '571-575 م',
        location: 'بادية بني سعد',
        category: 'الطفولة والشباب',
        iconName: 'child_care',
        order: 3,
      ),
      const SeerahEvent(
        id: '4',
        title: 'حادثة شق الصدر',
        subtitle: 'عند حليمة السعدية',
        description: 'جاء جبريل عليه السلام وشق صدر النبي ﷺ وأخرج قلبه وغسله في طست من ذهب مملوء إيماناً وحكمة، ثم أعاده. هذا الحدث المعجز أخاف حليمة فأعادته لأمه.',
        date: '575 م',
        location: 'بادية بني سعد',
        category: 'الطفولة والشباب',
        iconName: 'healing',
        order: 4,
      ),
      const SeerahEvent(
        id: '5',
        title: 'وفاة والدته آمنة',
        subtitle: 'في الأبواء',
        description: 'توفيت والدة النبي ﷺ آمنة بنت وهب في الأبواء وهي عائدة من زيارة أخوال أبيه في المدينة. كان عمره ﷺ ست سنوات، فأصبح يتيم الأبوين.',
        date: '577 م',
        location: 'الأبواء',
        category: 'الطفولة والشباب',
        iconName: 'heart_broken',
        order: 5,
      ),
      const SeerahEvent(
        id: '6',
        title: 'كفالة جده عبد المطلب',
        subtitle: 'بعد وفاة والدته',
        description: 'كفل جده عبد المطلب النبي ﷺ بعد وفاة والدته، وكان يحبه حباً شديداً ويقربه إليه أكثر من أولاده. كان يجلسه على فراشه ولا يأكل إلا معه.',
        date: '577-579 م',
        location: 'مكة المكرمة',
        category: 'الطفولة والشباب',
        iconName: 'elderly',
        order: 6,
      ),
      const SeerahEvent(
        id: '7',
        title: 'وفاة جده عبد المطلب',
        subtitle: 'وانتقال الكفالة لعمه أبي طالب',
        description: 'توفي جده عبد المطلب وعمر النبي ﷺ ثماني سنوات، فانتقلت كفالته إلى عمه أبي طالب الذي أحبه وحماه طوال حياته رغم عدم إسلامه.',
        date: '579 م',
        location: 'مكة المكرمة',
        category: 'الطفولة والشباب',
        iconName: 'family_restroom',
        order: 7,
      ),
      const SeerahEvent(
        id: '8',
        title: 'رحلة الشام الأولى',
        subtitle: 'مع عمه أبي طالب',
        description: 'سافر مع عمه أبي طالب في رحلة تجارية إلى الشام وعمره 12 سنة. التقى بالراهب بحيرا الذي تنبأ بنبوته ونصح أبا طالب بحمايته من اليهود.',
        date: '583 م',
        location: 'بصرى - الشام',
        category: 'الطفولة والشباب',
        iconName: 'travel_explore',
        order: 8,
      ),
      const SeerahEvent(
        id: '9',
        title: 'حرب الفجار',
        subtitle: 'مشاركته في الحرب',
        description: 'شارك النبي ﷺ في حرب الفجار بين قريش وهوازن وعمره 15 سنة. كان دوره محدوداً حيث كان ينبل عمومته (يجهز لهم النبال).',
        date: '586 م',
        location: 'عكاظ',
        category: 'الطفولة والشباب',
        iconName: 'security',
        order: 9,
      ),
      const SeerahEvent(
        id: '10',
        title: 'حلف الفضول',
        subtitle: 'المشاركة في حلف العدالة',
        description: 'شارك في حلف الفضول الذي تعاهدت فيه قبائل مكة على نصرة المظلوم ورد الحقوق. قال ﷺ لاحقاً: "لقد شهدت في دار عبد الله بن جدعان حلفاً ما أحب أن لي به حمر النعم".',
        date: '590 م',
        location: 'مكة المكرمة',
        category: 'الطفولة والشباب',
        iconName: 'balance',
        order: 10,
      ),

      // فترة ما قبل البعثة
      const SeerahEvent(
        id: '11',
        title: 'عمله في التجارة',
        subtitle: 'الصادق الأمين',
        description: 'اشتغل بالتجارة وعُرف بين قومه بالصادق الأمين لصدقه وأمانته. كان الناس يودعون عنده أماناتهم ويحتكمون إليه في خلافاتهم.',
        date: '590-595 م',
        location: 'مكة المكرمة',
        category: 'ما قبل البعثة',
        iconName: 'business',
        order: 11,
      ),
      const SeerahEvent(
        id: '12',
        title: 'تجارته لخديجة رضي الله عنها',
        subtitle: 'رحلة الشام الثانية',
        description: 'عمل في تجارة خديجة بنت خويلد وسافر بتجارتها إلى الشام. رافقه غلامها ميسرة الذي شهد كرامات ومعجزات في هذه الرحلة.',
        date: '595 م',
        location: 'الشام',
        category: 'ما قبل البعثة',
        iconName: 'local_shipping',
        order: 12,
      ),
      const SeerahEvent(
        id: '13',
        title: 'زواجه من خديجة رضي الله عنها',
        subtitle: 'أول زواج مبارك',
        description: 'تزوج من خديجة بنت خويلد وعمره 25 سنة وعمرها 40 سنة. كانت أول من آمن به وساندته بمالها ونفسها. رُزق منها بجميع أولاده إلا إبراهيم.',
        date: '595 م',
        location: 'مكة المكرمة',
        category: 'ما قبل البعثة',
        iconName: 'favorite',
        order: 13,
      ),
      const SeerahEvent(
        id: '14',
        title: 'بناء الكعبة وحكم الحجر الأسود',
        subtitle: 'حكمته في حل النزاع',
        description: 'عندما اختلفت قبائل قريش حول من يضع الحجر الأسود في مكانه عند بناء الكعبة، حكّموا النبي ﷺ فوضع الحجر في ثوب وطلب من كل قبيلة أن تمسك بطرف، ثم وضعه بيده.',
        date: '605 م',
        location: 'مكة المكرمة - الكعبة',
        category: 'ما قبل البعثة',
        iconName: 'architecture',
        order: 14,
      ),
      const SeerahEvent(
        id: '15',
        title: 'التحنث في غار حراء',
        subtitle: 'التعبد والتأمل',
        description: 'اعتاد النبي ﷺ الخلوة في غار حراء للتعبد والتأمل، خاصة في شهر رمضان. كان يتزود بالطعام ويمكث أياماً يتفكر في خلق الله وحال قومه.',
        date: '605-610 م',
        location: 'غار حراء',
        category: 'ما قبل البعثة',
        iconName: 'self_improvement',
        order: 15,
      ),

      // البعثة والدعوة
      const SeerahEvent(
        id: '16',
        title: 'بدء الوحي',
        subtitle: 'نزول سورة العلق',
        description: 'نزل جبريل عليه السلام على النبي ﷺ في غار حراء وقال له: "اقرأ"، فأنزل عليه أول آيات القرآن: "اقرأ باسم ربك الذي خلق". كان عمره ﷺ 40 سنة.',
        date: '610 م - 17 رمضان',
        location: 'غار حراء',
        category: 'البعثة والدعوة',
        iconName: 'auto_awesome',
        order: 16,
      ),
      const SeerahEvent(
        id: '17',
        title: 'إسلام خديجة رضي الله عنها',
        subtitle: 'أول من آمن',
        description: 'كانت خديجة رضي الله عنها أول من آمن بالنبي ﷺ وصدقته. ثبتته وطمأنته عندما جاءه الوحي أول مرة، وقالت: "كلا والله ما يخزيك الله أبداً".',
        date: '610 م',
        location: 'مكة المكرمة',
        category: 'البعثة والدعوة',
        iconName: 'person_add',
        order: 17,
      ),
      const SeerahEvent(
        id: '18',
        title: 'إسلام علي بن أبي طالب',
        subtitle: 'أول من أسلم من الصبيان',
        description: 'أسلم علي بن أبي طالب رضي الله عنه وعمره 10 سنوات، وكان أول من أسلم من الصبيان. نشأ في بيت النبي ﷺ وكان من أقرب الناس إليه.',
        date: '610 م',
        location: 'مكة المكرمة',
        category: 'البعثة والدعوة',
        iconName: 'child_friendly',
        order: 18,
      ),
      const SeerahEvent(
        id: '19',
        title: 'إسلام أبي بكر الصديق',
        subtitle: 'أول من أسلم من الرجال',
        description: 'أسلم أبو بكر الصديق رضي الله عنه وكان أول من أسلم من الرجال الأحرار. كان صديق النبي ﷺ قبل الإسلام وأول من صدقه في دعوته.',
        date: '610 م',
        location: 'مكة المكرمة',
        category: 'البعثة والدعوة',
        iconName: 'person_add',
        order: 19,
      ),
      const SeerahEvent(
        id: '20',
        title: 'الدعوة السرية',
        subtitle: 'ثلاث سنوات من الدعوة الخفية',
        description: 'دعا النبي ﷺ سراً لمدة ثلاث سنوات، يدعو الأقربين والأصدقاء. أسلم في هذه الفترة نحو 40 شخصاً من خيرة أهل مكة منهم عثمان وطلحة والزبير.',
        date: '610-613 م',
        location: 'مكة المكرمة',
        category: 'البعثة والدعوة',
        iconName: 'visibility_off',
        order: 20,
      ),

      // الدعوة الجهرية والاضطهاد
      const SeerahEvent(
        id: '21',
        title: 'الدعوة الجهرية',
        subtitle: 'وأنذر عشيرتك الأقربين',
        description: 'أمر الله تعالى النبي ﷺ بالجهر بالدعوة، فصعد على الصفا ونادى قبائل قريش وأعلن دعوته. قال أبو لهب: "تباً لك سائر اليوم، ألهذا جمعتنا؟"',
        date: '613 م',
        location: 'جبل الصفا - مكة',
        category: 'الدعوة الجهرية',
        iconName: 'campaign',
        order: 21,
      ),
      const SeerahEvent(
        id: '22',
        title: 'بداية الاضطهاد',
        subtitle: 'معاداة قريش للدعوة',
        description: 'بدأت قريش في اضطهاد المسلمين بعد الجهر بالدعوة. تعرض المسلمون للتعذيب والحصار الاقتصادي والاجتماعي، خاصة الضعفاء منهم.',
        date: '613-615 م',
        location: 'مكة المكرمة',
        category: 'الدعوة الجهرية',
        iconName: 'warning',
        order: 22,
      ),
      const SeerahEvent(
        id: '23',
        title: 'تعذيب المسلمين',
        subtitle: 'بلال وآل ياسر وغيرهم',
        description: 'تعرض المسلمون الضعفاء للتعذيب الشديد. عُذب بلال بن رباح في الرمضاء، وقُتل ياسر وزوجته سمية (أول شهيدة في الإسلام)، وعُذب عمار بن ياسر.',
        date: '613-615 م',
        location: 'مكة المكرمة',
        category: 'الدعوة الجهرية',
        iconName: 'healing',
        order: 23,
      ),
      const SeerahEvent(
        id: '24',
        title: 'الهجرة الأولى للحبشة',
        subtitle: 'هجرة المسلمين لأرض النجاشي',
        description: 'أذن النبي ﷺ للمسلمين بالهجرة إلى الحبشة فراراً من الاضطهاد. هاجر 83 رجلاً و19 امرأة بقيادة عثمان بن عفان وزوجته رقية بنت النبي ﷺ.',
        date: '615 م',
        location: 'الحبشة',
        category: 'الدعوة الجهرية',
        iconName: 'flight_takeoff',
        order: 24,
      ),
      const SeerahEvent(
        id: '25',
        title: 'إسلام حمزة بن عبد المطلب',
        subtitle: 'أسد الله وأسد رسوله',
        description: 'أسلم حمزة عم النبي ﷺ بعد أن سمع بإهانة أبي جهل للنبي ﷺ. كان إسلامه عزة للمسلمين وقوة لهم، ولُقب بأسد الله وأسد رسوله.',
        date: '615 م',
        location: 'مكة المكرمة',
        category: 'الدعوة الجهرية',
        iconName: 'shield',
        order: 25,
      ),
      const SeerahEvent(
        id: '26',
        title: 'إسلام عمر بن الخطاب',
        subtitle: 'الفاروق الذي أعز الله به الإسلام',
        description: 'أسلم عمر بن الخطاب بعد أن سمع القرآن في بيت أخته فاطمة. قال النبي ﷺ: "اللهم أعز الإسلام بأحب هذين الرجلين إليك: بأبي جهل أو بعمر بن الخطاب".',
        date: '616 م',
        location: 'مكة المكرمة',
        category: 'الدعوة الجهرية',
        iconName: 'gavel',
        order: 26,
      ),
      const SeerahEvent(
        id: '27',
        title: 'صحيفة المقاطعة',
        subtitle: 'حصار بني هاشم وبني المطلب',
        description: 'كتبت قريش صحيفة تقاطع فيها بني هاشم وبني المطلب، فلا يبايعونهم ولا يناكحونهم. استمر الحصار ثلاث سنوات في شعب أبي طالب.',
        date: '617-620 م',
        location: 'شعب أبي طالب - مكة',
        category: 'الدعوة الجهرية',
        iconName: 'block',
        order: 27,
      ),
      const SeerahEvent(
        id: '28',
        title: 'عام الحزن',
        subtitle: 'وفاة خديجة وأبي طالب',
        description: 'توفيت خديجة رضي الله عنها وأبو طالب في عام واحد، فسُمي عام الحزن. فقد النبي ﷺ أعظم مؤازريه: خديجة في البيت وأبو طالب في الحماية.',
        date: '620 م',
        location: 'مكة المكرمة',
        category: 'الدعوة الجهرية',
        iconName: 'sentiment_very_dissatisfied',
        order: 28,
      ),
      const SeerahEvent(
        id: '29',
        title: 'رحلة الطائف',
        subtitle: 'طلب النصرة من ثقيف',
        description: 'سافر النبي ﷺ إلى الطائف يطلب النصرة من ثقيف، لكنهم رفضوا وآذوه. رجع حزيناً ودعا دعاءه المشهور: "اللهم إليك أشكو ضعف قوتي..."',
        date: '620 م',
        location: 'الطائف',
        category: 'الدعوة الجهرية',
        iconName: 'directions_walk',
        order: 29,
      ),
      const SeerahEvent(
        id: '30',
        title: 'الإسراء والمعراج',
        subtitle: 'الرحلة المباركة إلى السماء',
        description: 'أُسري بالنبي ﷺ ليلاً من المسجد الحرام إلى المسجد الأقصى، ثم عُرج به إلى السماوات العلى حيث فُرضت عليه الصلوات الخمس.',
        date: '621 م - 27 رجب',
        location: 'مكة - القدس - السماوات',
        category: 'المعجزات',
        iconName: 'flight',
        order: 30,
      ),
    ];
  }
}
