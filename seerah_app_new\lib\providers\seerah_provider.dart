import 'package:flutter/foundation.dart';
import '../models/seerah_event.dart';

class SeerahProvider with ChangeNotifier {
  List<SeerahEvent> _events = [];
  List<SeerahEvent> _filteredEvents = [];
  String _selectedCategory = 'الكل';
  bool _isLoading = false;
  String _searchQuery = '';

  // Getters
  List<SeerahEvent> get events => _filteredEvents;
  List<SeerahEvent> get allEvents => _events;
  String get selectedCategory => _selectedCategory;
  bool get isLoading => _isLoading;
  String get searchQuery => _searchQuery;

  // الحصول على الفئات المتاحة
  List<String> get categories {
    final cats = _events.map((e) => e.category).toSet().toList();
    cats.insert(0, 'الكل');
    return cats;
  }

  // الحصول على عدد الأحداث في كل فئة
  Map<String, int> get categoryCounts {
    final counts = <String, int>{};
    for (final event in _events) {
      counts[event.category] = (counts[event.category] ?? 0) + 1;
    }
    return counts;
  }

  // تحميل البيانات الأولية
  Future<void> loadInitialData() async {
    _isLoading = true;
    notifyListeners();

    try {
      // محاكاة تحميل البيانات
      await Future.delayed(const Duration(milliseconds: 500));
      
      _events = _getSampleEvents();
      _applyFilters();
      
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات السيرة: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // تطبيق الفلاتر
  void _applyFilters() {
    _filteredEvents = _events.where((event) {
      final matchesCategory = _selectedCategory == 'الكل' || 
                             event.category == _selectedCategory;
      final matchesSearch = _searchQuery.isEmpty ||
                           event.title.contains(_searchQuery) ||
                           event.subtitle.contains(_searchQuery) ||
                           event.description.contains(_searchQuery);
      
      return matchesCategory && matchesSearch && event.isAvailable;
    }).toList();

    // ترتيب حسب الترتيب المحدد
    _filteredEvents.sort((a, b) => a.order.compareTo(b.order));
  }

  // تغيير الفئة المختارة
  void setCategory(String category) {
    if (_selectedCategory != category) {
      _selectedCategory = category;
      _applyFilters();
      notifyListeners();
    }
  }

  // تحديث البحث
  void updateSearch(String query) {
    if (_searchQuery != query) {
      _searchQuery = query;
      _applyFilters();
      notifyListeners();
    }
  }

  // الحصول على حدث بالمعرف
  SeerahEvent? getEventById(String id) {
    try {
      return _events.firstWhere((event) => event.id == id);
    } catch (e) {
      return null;
    }
  }

  // إضافة حدث جديد
  void addEvent(SeerahEvent event) {
    _events.add(event);
    _applyFilters();
    notifyListeners();
  }

  // تحديث حدث
  void updateEvent(SeerahEvent updatedEvent) {
    final index = _events.indexWhere((event) => event.id == updatedEvent.id);
    if (index != -1) {
      _events[index] = updatedEvent;
      _applyFilters();
      notifyListeners();
    }
  }

  // حذف حدث
  void removeEvent(String id) {
    _events.removeWhere((event) => event.id == id);
    _applyFilters();
    notifyListeners();
  }

  // بيانات تجريبية
  List<SeerahEvent> _getSampleEvents() {
    return [
      const SeerahEvent(
        id: '1',
        title: 'مولد النبي ﷺ',
        subtitle: 'عام الفيل - مكة المكرمة',
        description: 'وُلد النبي محمد ﷺ في مكة المكرمة في عام الفيل، وهو العام الذي حاول فيه أبرهة الحبشي هدم الكعبة المشرفة.',
        date: '571 م',
        location: 'مكة المكرمة',
        category: 'الطفولة والشباب',
        iconName: 'star',
        order: 1,
      ),
      const SeerahEvent(
        id: '2',
        title: 'وفاة والده عبد الله',
        subtitle: 'قبل ولادته ﷺ',
        description: 'توفي والد النبي ﷺ عبد الله بن عبد المطلب قبل ولادته، فنشأ يتيم الأب.',
        date: '570 م',
        location: 'المدينة المنورة',
        category: 'الطفولة والشباب',
        iconName: 'heart_broken',
        order: 0,
      ),
      const SeerahEvent(
        id: '3',
        title: 'الرضاعة عند حليمة السعدية',
        subtitle: 'في بادية بني سعد',
        description: 'أرضعته حليمة السعدية في بادية بني سعد، حيث نشأ في بيئة صحراوية نقية.',
        date: '571-575 م',
        location: 'بادية بني سعد',
        category: 'الطفولة والشباب',
        iconName: 'child_care',
        order: 2,
      ),
    ];
  }
}
