// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:seerah_app_new/main.dart';

void main() {
  testWidgets('Seerah app basic test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const SeerahApp());

    // Verify that the app title is displayed.
    expect(find.text('سيرة النبي محمد ﷺ'), findsWidgets);

    // Verify that the mosque icon is displayed.
    expect(find.byIcon(Icons.mosque), findsOneWidget);

    // Verify that the welcome message is displayed.
    expect(find.text('تطبيق شامل لتعليم السيرة النبوية الشريفة'), findsOneWidget);
  });
}
