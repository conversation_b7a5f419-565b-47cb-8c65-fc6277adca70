import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart' as theme_provider;
import '../providers/favorites_provider.dart';
import '../providers/app_provider.dart';
import '../theme/app_theme.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer3<theme_provider.ThemeProvider, FavoritesProvider, AppProvider>(
      builder: (context, themeProvider, favoritesProvider, appProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              'الإعدادات',
              style: themeProvider.applyFontSize(
                Theme.of(context).textTheme.titleLarge!,
              ),
            ),
            backgroundColor: themeProvider.isDarkMode
                ? AppTheme.primaryDark
                : AppTheme.primaryLight,
            foregroundColor: themeProvider.isDarkMode
                ? AppTheme.onPrimaryDark
                : AppTheme.onPrimaryLight,
            centerTitle: true,
            elevation: 0,
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: AppTheme.getPrimaryGradient(themeProvider.isDarkMode),
            ),
            child: Column(
              children: [
                const SizedBox(height: AppTheme.mediumPadding),

                // أيقونة الإعدادات
                Container(
                  padding: const EdgeInsets.all(AppTheme.largePadding),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(
                    Icons.settings_rounded,
                    size: AppTheme.extraLargeIconSize + 16,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: AppTheme.mediumPadding),

                // العنوان
                Text(
                  'إعدادات التطبيق',
                  style: themeProvider.applyFontSize(
                    const TextStyle(
                      fontSize: AppTheme.headline3Size,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppTheme.extraLargePadding),

                // محتوى الإعدادات
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: AppTheme.mediumPadding),
                    padding: const EdgeInsets.all(AppTheme.mediumPadding),
                    decoration: BoxDecoration(
                      color: Theme.of(context).scaffoldBackgroundColor,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(AppTheme.largePadding),
                        topRight: Radius.circular(AppTheme.largePadding),
                      ),
                    ),
                    child: ListView(
                      children: [
                        // قسم المظهر
                        _buildSectionHeader('المظهر والعرض', Icons.palette, themeProvider),
                        _buildThemeSettings(themeProvider),
                        _buildFontSizeSettings(themeProvider),
                        _buildHighContrastSettings(themeProvider),

                        const SizedBox(height: AppTheme.largePadding),

                        // قسم المحتوى
                        _buildSectionHeader('المحتوى', Icons.library_books, themeProvider),
                        _buildFavoritesInfo(favoritesProvider, themeProvider),
                        _buildContentStats(appProvider, themeProvider),

                        const SizedBox(height: AppTheme.largePadding),

                        // قسم التطبيق
                        _buildSectionHeader('معلومات التطبيق', Icons.info, themeProvider),
                        _buildAppInfo(themeProvider),
                        _buildDeveloperInfo(themeProvider),

                        const SizedBox(height: AppTheme.largePadding),

                        // قسم الإجراءات
                        _buildSectionHeader('الإجراءات', Icons.build, themeProvider),
                        _buildResetSettings(themeProvider, favoritesProvider, appProvider),

                        const SizedBox(height: AppTheme.extraLargePadding),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSectionHeader(String title, IconData icon, theme_provider.ThemeProvider themeProvider) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.mediumPadding),
      child: Row(
        children: [
          Icon(
            icon,
            color: themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight,
            size: AppTheme.mediumIconSize,
          ),
          const SizedBox(width: AppTheme.smallPadding),
          Text(
            title,
            style: themeProvider.applyFontSize(
              TextStyle(
                fontSize: AppTheme.headline5Size,
                fontWeight: FontWeight.bold,
                color: themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildThemeSettings(theme_provider.ThemeProvider themeProvider) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: AppTheme.mediumRadius),
      child: ListTile(
        leading: Icon(
          themeProvider.currentThemeIcon,
          color: themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight,
        ),
        title: Text(
          'وضع المظهر',
          style: themeProvider.applyFontSize(
            const TextStyle(
              fontSize: AppTheme.bodyText1Size,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        subtitle: Text(
          themeProvider.currentThemeName,
          style: themeProvider.applyFontSize(
            const TextStyle(fontSize: AppTheme.bodyText2Size),
          ),
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: AppTheme.smallIconSize),
        onTap: () => _showThemeDialog(themeProvider),
      ),
    );
  }

  Widget _buildFontSizeSettings(theme_provider.ThemeProvider themeProvider) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: AppTheme.mediumRadius),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.mediumPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.text_fields,
                  color: themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight,
                ),
                const SizedBox(width: AppTheme.smallPadding),
                Text(
                  'حجم الخط',
                  style: themeProvider.applyFontSize(
                    const TextStyle(
                      fontSize: AppTheme.bodyText1Size,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  themeProvider.fontSizeDescription,
                  style: themeProvider.applyFontSize(
                    const TextStyle(
                      fontSize: AppTheme.bodyText2Size,
                      color: Colors.grey,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.smallPadding),
            Row(
              children: [
                IconButton(
                  onPressed: themeProvider.fontSize > 0.8
                      ? () => themeProvider.decreaseFontSize()
                      : null,
                  icon: const Icon(Icons.remove),
                ),
                Expanded(
                  child: Slider(
                    value: themeProvider.fontSize,
                    min: 0.8,
                    max: 1.5,
                    divisions: 7,
                    onChanged: (value) => themeProvider.setFontSize(value),
                  ),
                ),
                IconButton(
                  onPressed: themeProvider.fontSize < 1.5
                      ? () => themeProvider.increaseFontSize()
                      : null,
                  icon: const Icon(Icons.add),
                ),
              ],
            ),
            Center(
              child: TextButton(
                onPressed: () => themeProvider.resetFontSize(),
                child: const Text('إعادة تعيين'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHighContrastSettings(theme_provider.ThemeProvider themeProvider) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: AppTheme.mediumRadius),
      child: SwitchListTile(
        secondary: Icon(
          Icons.contrast,
          color: themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight,
        ),
        title: Text(
          'التباين العالي',
          style: themeProvider.applyFontSize(
            const TextStyle(
              fontSize: AppTheme.bodyText1Size,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        subtitle: Text(
          'تحسين الرؤية للأشخاص ذوي الإعاقة البصرية',
          style: themeProvider.applyFontSize(
            const TextStyle(fontSize: AppTheme.bodyText2Size),
          ),
        ),
        value: themeProvider.isHighContrast,
        onChanged: (value) => themeProvider.toggleHighContrast(),
      ),
    );
  }

  Widget _buildFavoritesInfo(FavoritesProvider favoritesProvider, theme_provider.ThemeProvider themeProvider) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: AppTheme.mediumRadius),
      child: ListTile(
        leading: const Icon(Icons.favorite, color: Colors.red),
        title: Text(
          'المفضلة',
          style: themeProvider.applyFontSize(
            const TextStyle(
              fontSize: AppTheme.bodyText1Size,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        subtitle: Text(
          '${favoritesProvider.totalCount} عنصر مفضل',
          style: themeProvider.applyFontSize(
            const TextStyle(fontSize: AppTheme.bodyText2Size),
          ),
        ),
        trailing: favoritesProvider.hasFavorites
            ? TextButton(
                onPressed: () => _clearFavorites(favoritesProvider),
                child: const Text('مسح الكل'),
              )
            : null,
      ),
    );
  }

  Widget _buildContentStats(AppProvider appProvider, theme_provider.ThemeProvider themeProvider) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: AppTheme.mediumRadius),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.mediumPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.analytics, color: AppTheme.info),
                const SizedBox(width: AppTheme.smallPadding),
                Text(
                  'إحصائيات المحتوى',
                  style: themeProvider.applyFontSize(
                    const TextStyle(
                      fontSize: AppTheme.bodyText1Size,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.smallPadding),
            _buildStatRow('أحداث السيرة', '30 حدث', Icons.book, themeProvider),
            _buildStatRow('الأحاديث النبوية', '15 حديث', Icons.format_quote, themeProvider),
            _buildStatRow('الصحابة الكرام', 'قريباً', Icons.people, themeProvider),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String title, String value, IconData icon, theme_provider.ThemeProvider themeProvider) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppTheme.extraSmallPadding),
      child: Row(
        children: [
          Icon(icon, size: AppTheme.smallIconSize, color: Colors.grey),
          const SizedBox(width: AppTheme.smallPadding),
          Text(
            title,
            style: themeProvider.applyFontSize(
              const TextStyle(fontSize: AppTheme.bodyText2Size),
            ),
          ),
          const Spacer(),
          Text(
            value,
            style: themeProvider.applyFontSize(
              const TextStyle(
                fontSize: AppTheme.bodyText2Size,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppInfo(theme_provider.ThemeProvider themeProvider) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: AppTheme.mediumRadius),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.mediumPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info, color: AppTheme.info),
                const SizedBox(width: AppTheme.smallPadding),
                Text(
                  'معلومات التطبيق',
                  style: themeProvider.applyFontSize(
                    const TextStyle(
                      fontSize: AppTheme.bodyText1Size,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.smallPadding),
            _buildInfoRow('اسم التطبيق', 'سيرة النبي محمد ﷺ', themeProvider),
            _buildInfoRow('الإصدار', '1.0.0', themeProvider),
            _buildInfoRow('تاريخ البناء', '2025', themeProvider),
            _buildInfoRow('المنصة', 'Flutter', themeProvider),
          ],
        ),
      ),
    );
  }

  Widget _buildDeveloperInfo(theme_provider.ThemeProvider themeProvider) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: AppTheme.mediumRadius),
      child: ListTile(
        leading: const Icon(Icons.person, color: AppTheme.success),
        title: Text(
          'المطور',
          style: themeProvider.applyFontSize(
            const TextStyle(
              fontSize: AppTheme.bodyText1Size,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        subtitle: Text(
          'Wael Shaibi 2025',
          style: themeProvider.applyFontSize(
            const TextStyle(fontSize: AppTheme.bodyText2Size),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String title, String value, theme_provider.ThemeProvider themeProvider) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppTheme.extraSmallPadding),
      child: Row(
        children: [
          Text(
            title,
            style: themeProvider.applyFontSize(
              const TextStyle(fontSize: AppTheme.bodyText2Size),
            ),
          ),
          const Spacer(),
          Text(
            value,
            style: themeProvider.applyFontSize(
              const TextStyle(
                fontSize: AppTheme.bodyText2Size,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResetSettings(
    theme_provider.ThemeProvider themeProvider,
    FavoritesProvider favoritesProvider,
    AppProvider appProvider,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: AppTheme.mediumRadius),
      child: ListTile(
        leading: const Icon(Icons.restore, color: AppTheme.warning),
        title: Text(
          'إعادة تعيين الإعدادات',
          style: themeProvider.applyFontSize(
            const TextStyle(
              fontSize: AppTheme.bodyText1Size,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        subtitle: Text(
          'إعادة جميع الإعدادات للقيم الافتراضية',
          style: themeProvider.applyFontSize(
            const TextStyle(fontSize: AppTheme.bodyText2Size),
          ),
        ),
        onTap: () => _resetAllSettings(themeProvider, favoritesProvider, appProvider),
      ),
    );
  }

  void _showThemeDialog(theme_provider.ThemeProvider themeProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار وضع المظهر'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<theme_provider.ThemeMode>(
              title: const Text('الوضع النهاري'),
              subtitle: const Text('مظهر فاتح دائماً'),
              value: theme_provider.ThemeMode.light,
              groupValue: themeProvider.themeMode,
              onChanged: (value) {
                if (value != null) {
                  themeProvider.setThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<theme_provider.ThemeMode>(
              title: const Text('الوضع الليلي'),
              subtitle: const Text('مظهر مظلم دائماً'),
              value: theme_provider.ThemeMode.dark,
              groupValue: themeProvider.themeMode,
              onChanged: (value) {
                if (value != null) {
                  themeProvider.setThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<theme_provider.ThemeMode>(
              title: const Text('وضع النظام'),
              subtitle: const Text('يتبع إعدادات النظام'),
              value: theme_provider.ThemeMode.system,
              groupValue: themeProvider.themeMode,
              onChanged: (value) {
                if (value != null) {
                  themeProvider.setThemeMode(value);
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _clearFavorites(FavoritesProvider favoritesProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح جميع المفضلة'),
        content: const Text('هل تريد مسح جميع العناصر المفضلة؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              favoritesProvider.clearAllFavorites();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم مسح جميع المفضلة'),
                  duration: Duration(seconds: 2),
                ),
              );
            },
            child: const Text('مسح الكل'),
          ),
        ],
      ),
    );
  }

  void _resetAllSettings(
    theme_provider.ThemeProvider themeProvider,
    FavoritesProvider favoritesProvider,
    AppProvider appProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين الإعدادات'),
        content: const Text(
          'هل تريد إعادة جميع الإعدادات للقيم الافتراضية؟ '
          'سيتم مسح المفضلة وإعادة تعيين المظهر وحجم الخط.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              await themeProvider.resetSettings();
              await favoritesProvider.clearAllFavorites();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إعادة تعيين جميع الإعدادات'),
                  duration: Duration(seconds: 3),
                ),
              );
            },
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
    );
  }
}
