import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/seerah_provider.dart';
import '../providers/favorites_provider.dart';
import '../models/seerah_event.dart';
import 'filter_screen.dart';

class SeerahScreen extends StatelessWidget {
  const SeerahScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'السيرة النبوية',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF4CAF50),
        centerTitle: true,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const FilterScreen(type: 'seerah'),
                ),
              );
            },
            icon: const Icon(
              Icons.filter_list_rounded,
              color: Colors.white,
            ),
            tooltip: 'فلترة النتائج',
          ),
        ],
      ),
      body: Consumer2<SeerahProvider, FavoritesProvider>(
        builder: (context, seerahProvider, favoritesProvider, child) {
          return Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xFF4CAF50),
                Color(0xFF81C784),
              ],
            ),
          ),
          child: Column(
            children: [
              // Header Section
              Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    const SizedBox(height: 20),

                    // أيقونة القسم
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const Icon(
                        Icons.book_rounded,
                        size: 80,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 20),

                    // عنوان القسم
                    const Text(
                      'السيرة النبوية الشريفة',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 10),

                    // وصف القسم مع الإحصائيات
                    Text(
                      'تعرف على ${seerahProvider.events.length} حدث مهم من حياة النبي محمد ﷺ',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              // Content Section
              Expanded(
                child: seerahProvider.isLoading
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(color: Colors.white),
                            SizedBox(height: 16),
                            Text(
                              'جاري تحميل أحداث السيرة...',
                              style: TextStyle(color: Colors.white),
                            ),
                          ],
                        ),
                      )
                    : seerahProvider.events.isEmpty
                        ? const Center(
                            child: Text(
                              'لا توجد أحداث متاحة حالياً',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                              ),
                            ),
                          )
                        : ListView.builder(
                            padding: const EdgeInsets.all(20),
                            itemCount: seerahProvider.events.length + 1,
                            itemBuilder: (context, index) {
                              if (index == seerahProvider.events.length) {
                                // إضافة كارت الإحصائيات في النهاية
                                return _buildStatisticsCard(seerahProvider);
                              }

                              final event = seerahProvider.events[index];
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 16),
                                child: _buildEventCard(event, favoritesProvider, context),
                              );
                            },
                          ),
              ),
            ],
          ),
        );
        },
      ),
    );
  }

  Widget _buildEventCard(SeerahEvent event, FavoritesProvider favoritesProvider, BuildContext context) {
    IconData iconData;
    switch (event.iconName) {
      case 'star':
        iconData = Icons.star_rounded;
        break;
      case 'heart_broken':
        iconData = Icons.heart_broken_rounded;
        break;
      case 'child_care':
        iconData = Icons.child_care_rounded;
        break;
      case 'healing':
        iconData = Icons.healing_rounded;
        break;
      case 'elderly':
        iconData = Icons.elderly_rounded;
        break;
      case 'family_restroom':
        iconData = Icons.family_restroom_rounded;
        break;
      case 'travel_explore':
        iconData = Icons.travel_explore_rounded;
        break;
      case 'security':
        iconData = Icons.security_rounded;
        break;
      case 'balance':
        iconData = Icons.balance_rounded;
        break;
      case 'business':
        iconData = Icons.business_rounded;
        break;
      case 'local_shipping':
        iconData = Icons.local_shipping_rounded;
        break;
      case 'favorite':
        iconData = Icons.favorite_rounded;
        break;
      case 'architecture':
        iconData = Icons.architecture_rounded;
        break;
      case 'self_improvement':
        iconData = Icons.self_improvement_rounded;
        break;
      case 'auto_awesome':
        iconData = Icons.auto_awesome_rounded;
        break;
      case 'person_add':
        iconData = Icons.person_add_rounded;
        break;
      case 'child_friendly':
        iconData = Icons.child_friendly_rounded;
        break;
      case 'visibility_off':
        iconData = Icons.visibility_off_rounded;
        break;
      case 'campaign':
        iconData = Icons.campaign_rounded;
        break;
      case 'warning':
        iconData = Icons.warning_rounded;
        break;
      case 'flight_takeoff':
        iconData = Icons.flight_takeoff_rounded;
        break;
      case 'shield':
        iconData = Icons.shield_rounded;
        break;
      case 'gavel':
        iconData = Icons.gavel_rounded;
        break;
      case 'block':
        iconData = Icons.block_rounded;
        break;
      case 'sentiment_very_dissatisfied':
        iconData = Icons.sentiment_very_dissatisfied_rounded;
        break;
      case 'directions_walk':
        iconData = Icons.directions_walk_rounded;
        break;
      case 'flight':
        iconData = Icons.flight_rounded;
        break;
      default:
        iconData = Icons.event_rounded;
    }

    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: event.isAvailable ? Colors.white : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(16),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: event.isAvailable
                        ? const Color(0xFF4CAF50).withValues(alpha: 0.1)
                        : Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    iconData,
                    size: 32,
                    color: event.isAvailable
                        ? const Color(0xFF4CAF50)
                        : Colors.grey.shade600,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        event.title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: event.isAvailable
                              ? const Color(0xFF424242)
                              : Colors.grey.shade600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        event.subtitle,
                        style: TextStyle(
                          fontSize: 14,
                          color: event.isAvailable
                              ? const Color(0xFF757575)
                              : Colors.grey.shade500,
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      onPressed: () {
                        favoritesProvider.toggleSeerahFavorite(event);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              favoritesProvider.isFavorite(event.id)
                                  ? 'تم إضافة الحدث للمفضلة'
                                  : 'تم إزالة الحدث من المفضلة',
                            ),
                            duration: const Duration(seconds: 2),
                          ),
                        );
                      },
                      icon: Icon(
                        favoritesProvider.isFavorite(event.id)
                            ? Icons.favorite
                            : Icons.favorite_border,
                        color: favoritesProvider.isFavorite(event.id)
                            ? Colors.red
                            : Colors.grey,
                      ),
                      tooltip: favoritesProvider.isFavorite(event.id)
                          ? 'إزالة من المفضلة'
                          : 'إضافة للمفضلة',
                    ),
                    Icon(
                      event.isAvailable ? Icons.arrow_forward_ios : Icons.schedule,
                      color: event.isAvailable
                          ? const Color(0xFF4CAF50)
                          : Colors.grey.shade400,
                      size: 20,
                    ),
                  ],
                ),
              ],
            ),
            if (event.description.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                event.description,
                style: TextStyle(
                  fontSize: 13,
                  color: event.isAvailable
                      ? const Color(0xFF666666)
                      : Colors.grey.shade500,
                  height: 1.4,
                ),
              ),
            ],
            if (event.date.isNotEmpty || event.location.isNotEmpty) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  if (event.date.isNotEmpty) ...[
                    Icon(
                      Icons.calendar_today,
                      size: 14,
                      color: Colors.grey.shade600,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      event.date,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                  if (event.date.isNotEmpty && event.location.isNotEmpty)
                    const SizedBox(width: 16),
                  if (event.location.isNotEmpty) ...[
                    Icon(
                      Icons.location_on,
                      size: 14,
                      color: Colors.grey.shade600,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      event.location,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsCard(SeerahProvider provider) {
    final categoryCounts = provider.categoryCounts;

    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            const Icon(
              Icons.analytics_rounded,
              size: 40,
              color: Color(0xFF4CAF50),
            ),
            const SizedBox(height: 12),
            const Text(
              'إحصائيات السيرة النبوية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF424242),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ...categoryCounts.entries.map((entry) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    entry.key,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF424242),
                    ),
                  ),
                  Text(
                    '${entry.value} أحداث',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF4CAF50),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }
}
