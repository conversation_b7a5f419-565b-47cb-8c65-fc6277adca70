import 'package:flutter/material.dart';

class SeerahScreen extends StatelessWidget {
  const SeerahScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF4CAF50),
            Color(0xFF81C784),
          ],
        ),
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            const SizedBox(height: 20),

            // أيقونة القسم
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.book_rounded,
                size: 80,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 20),

            // عنوان القسم
            const Text(
              'السيرة النبوية الشريفة',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 10),

            // وصف القسم
            const Text(
              'تعرف على أهم الأحداث في حياة النبي محمد ﷺ',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 40),

            // كروت الأحداث (عينة)
            _buildEventCard(
              title: 'مولد النبي ﷺ',
              subtitle: 'عام الفيل - مكة المكرمة',
              icon: Icons.star_rounded,
              isComingSoon: false,
            ),
            const SizedBox(height: 16),

            _buildEventCard(
              title: 'بدء الوحي',
              subtitle: 'نزول سورة العلق في غار حراء',
              icon: Icons.auto_awesome_rounded,
              isComingSoon: false,
            ),
            const SizedBox(height: 16),

            _buildEventCard(
              title: 'الهجرة إلى المدينة',
              subtitle: 'مع أبي بكر الصديق رضي الله عنه',
              icon: Icons.directions_walk_rounded,
              isComingSoon: false,
            ),
            const SizedBox(height: 16),

            _buildEventCard(
              title: 'المزيد من الأحداث',
              subtitle: 'سيتم إضافة 27 حدث آخر قريباً',
              icon: Icons.more_horiz_rounded,
              isComingSoon: true,
            ),
            const SizedBox(height: 40),

            // رسالة تطوير
            Card(
              elevation: 8,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Padding(
                padding: EdgeInsets.all(20),
                child: Column(
                  children: [
                    Icon(
                      Icons.construction_rounded,
                      size: 40,
                      color: Color(0xFF4CAF50),
                    ),
                    SizedBox(height: 12),
                    Text(
                      'قسم السيرة النبوية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF424242),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'سيتم إضافة 30 حدث مهم من السيرة النبوية مع التفاصيل الكاملة',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF757575),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEventCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool isComingSoon,
  }) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: isComingSoon ? Colors.grey.shade100 : Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isComingSoon
                    ? Colors.grey.shade300
                    : const Color(0xFF4CAF50).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                size: 32,
                color: isComingSoon
                    ? Colors.grey.shade600
                    : const Color(0xFF4CAF50),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: isComingSoon
                          ? Colors.grey.shade600
                          : const Color(0xFF424242),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: isComingSoon
                          ? Colors.grey.shade500
                          : const Color(0xFF757575),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              isComingSoon ? Icons.schedule : Icons.arrow_forward_ios,
              color: isComingSoon
                  ? Colors.grey.shade400
                  : const Color(0xFF4CAF50),
              size: 20,
            ),
          ],
        ),
      ),
    );
  }
}
