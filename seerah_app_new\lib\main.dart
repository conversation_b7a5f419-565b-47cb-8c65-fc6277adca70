import 'package:flutter/material.dart';

void main() {
  runApp(const SeerahApp());
}

class SeerahApp extends StatelessWidget {
  const SeerahApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'سيرة النبي محمد ﷺ',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        // ألوان أساسية بسيطة للبداية
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF1976D2), // أزرق أساسي
          brightness: Brightness.light,
        ),
        useMaterial3: true,
        // دعم اللغة العربية
        fontFamily: 'Arial', // خط بسيط يدعم العربية
      ),
      home: const HomeScreen(),
    );
  }
}

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'سيرة النبي محمد ﷺ',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF1976D2),
        centerTitle: true,
        elevation: 0,
      ),
      body: Container(
        width: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1976D2),
              Color(0xFF42A5F5),
            ],
          ),
        ),
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // أيقونة المسجد
              Icon(
                Icons.mosque,
                size: 80,
                color: Colors.white,
              ),
              SizedBox(height: 20),
              // العنوان الرئيسي
              Text(
                'سيرة النبي محمد ﷺ',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 10),
              // النص التوضيحي
              Text(
                'تطبيق شامل لتعليم السيرة النبوية الشريفة',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white70,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 40),
              // رسالة ترحيبية
              Card(
                margin: EdgeInsets.symmetric(horizontal: 32),
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: Text(
                    'مرحباً بك في تطبيق السيرة النبوية\nسنبدأ قريباً بإضافة المحتوى',
                    style: TextStyle(
                      fontSize: 16,
                      color: Color(0xFF424242),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
