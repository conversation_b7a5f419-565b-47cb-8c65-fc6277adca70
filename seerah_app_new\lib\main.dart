import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'screens/main_screen.dart';
import 'providers/app_provider.dart';
import 'providers/seerah_provider.dart';
import 'providers/hadith_provider.dart';

void main() {
  runApp(const SeerahApp());
}

class SeerahApp extends StatelessWidget {
  const SeerahApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AppProvider()),
        ChangeNotifierProvider(create: (_) => SeerahProvider()),
        ChangeNotifierProvider(create: (_) => HadithProvider()),
      ],
      child: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          return MaterialApp(
            title: 'سيرة النبي محمد ﷺ',
            debugShowCheckedModeBanner: false,
            theme: ThemeData(
              colorScheme: ColorScheme.fromSeed(
                seedColor: const Color(0xFF1976D2),
                brightness: appProvider.isDarkMode ? Brightness.dark : Brightness.light,
              ),
              useMaterial3: true,
              fontFamily: 'Arial',
              textTheme: TextTheme(
                bodyLarge: TextStyle(fontSize: appProvider.fontSize),
                bodyMedium: TextStyle(fontSize: appProvider.fontSize - 2),
                titleLarge: TextStyle(fontSize: appProvider.fontSize + 4),
              ),
            ),
            home: const AppInitializer(),
          );
        },
      ),
    );
  }
}

class AppInitializer extends StatefulWidget {
  const AppInitializer({super.key});

  @override
  State<AppInitializer> createState() => _AppInitializerState();
}

class _AppInitializerState extends State<AppInitializer> {
  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    final appProvider = Provider.of<AppProvider>(context, listen: false);
    final seerahProvider = Provider.of<SeerahProvider>(context, listen: false);
    final hadithProvider = Provider.of<HadithProvider>(context, listen: false);

    try {
      // تحميل الإعدادات
      await appProvider.loadSettings();

      // تحميل البيانات
      await Future.wait([
        seerahProvider.loadInitialData(),
        hadithProvider.loadInitialData(),
      ]);

      // تحديث الإحصائيات
      appProvider.updateStatistics(
        seerahEvents: seerahProvider.allEvents.length,
        hadiths: hadithProvider.allHadiths.length,
      );

    } catch (e) {
      appProvider.setError('خطأ في تحميل التطبيق: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        if (appProvider.isLoading) {
          return const Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري تحميل التطبيق...'),
                ],
              ),
            ),
          );
        }

        return const MainScreen();
      },
    );
  }
}


