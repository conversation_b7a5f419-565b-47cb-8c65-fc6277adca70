import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/seerah_event.dart';
import '../models/hadith.dart';

enum FavoriteType { seerah, hadith }

class FavoriteItem {
  final String id;
  final String title;
  final String subtitle;
  final FavoriteType type;
  final DateTime addedAt;
  final Map<String, dynamic> data;

  const FavoriteItem({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.type,
    required this.addedAt,
    required this.data,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'subtitle': subtitle,
      'type': type.index,
      'addedAt': addedAt.millisecondsSinceEpoch,
      'data': data,
    };
  }

  factory FavoriteItem.fromJson(Map<String, dynamic> json) {
    return FavoriteItem(
      id: json['id'],
      title: json['title'],
      subtitle: json['subtitle'],
      type: FavoriteType.values[json['type']],
      addedAt: DateTime.fromMillisecondsSinceEpoch(json['addedAt']),
      data: Map<String, dynamic>.from(json['data']),
    );
  }

  factory FavoriteItem.fromSeerahEvent(SeerahEvent event) {
    return FavoriteItem(
      id: event.id,
      title: event.title,
      subtitle: event.subtitle,
      type: FavoriteType.seerah,
      addedAt: DateTime.now(),
      data: event.toJson(),
    );
  }

  factory FavoriteItem.fromHadith(Hadith hadith) {
    return FavoriteItem(
      id: hadith.id,
      title: hadith.category,
      subtitle: hadith.arabicText.length > 50
          ? '${hadith.arabicText.substring(0, 50)}...'
          : hadith.arabicText,
      type: FavoriteType.hadith,
      addedAt: DateTime.now(),
      data: hadith.toJson(),
    );
  }

  SeerahEvent toSeerahEvent() {
    return SeerahEvent.fromJson(data);
  }

  Hadith toHadith() {
    return Hadith.fromJson(data);
  }
}

class FavoritesProvider with ChangeNotifier {
  List<FavoriteItem> _favorites = [];
  bool _isLoading = false;
  bool _isInitialized = false;

  // Getters
  List<FavoriteItem> get favorites {
    return List.unmodifiable(_favorites);
  }
  List<FavoriteItem> get seerahFavorites =>
      _favorites.where((item) => item.type == FavoriteType.seerah).toList();
  List<FavoriteItem> get hadithFavorites =>
      _favorites.where((item) => item.type == FavoriteType.hadith).toList();

  bool get isLoading => _isLoading;
  int get totalCount => _favorites.length;
  int get seerahCount => seerahFavorites.length;
  int get hadithCount => hadithFavorites.length;

  // تحميل صامت للمفضلة (بدون إشعارات)
  Future<void> _loadFavoritesQuietly() async {
    if (_isInitialized || _isLoading) return;

    _isLoading = true;

    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = prefs.getStringList('favorites') ?? [];

      _favorites = favoritesJson
          .map((jsonString) {
            try {
              final json = jsonDecode(jsonString);
              return FavoriteItem.fromJson(json);
            } catch (e) {
              debugPrint('خطأ في تحليل عنصر مفضل: $e');
              return null;
            }
          })
          .where((item) => item != null)
          .cast<FavoriteItem>()
          .toList();

      // ترتيب حسب تاريخ الإضافة (الأحدث أولاً)
      _favorites.sort((a, b) => b.addedAt.compareTo(a.addedAt));
    } catch (e) {
      debugPrint('خطأ في تحميل المفضلة: $e');
      _favorites = [];
    }

    _isLoading = false;
    _isInitialized = true;
    // لا نرسل إشعارات من هنا لتجنب مشاكل البناء
  }

  // تحميل المفضلة من التخزين المحلي (للاستخدام العام)
  Future<void> loadFavorites() async {
    if (_isInitialized) {
      return; // البيانات محملة بالفعل
    }
    await _loadFavoritesQuietly();
    // تأخير الإشعار لتجنب مشاكل البناء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  // حفظ المفضلة في التخزين المحلي
  Future<void> _saveFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = _favorites
          .map((item) => jsonEncode(item.toJson()))
          .toList();

      await prefs.setStringList('favorites', favoritesJson);
    } catch (e) {
      debugPrint('خطأ في حفظ المفضلة: $e');
    }
  }

  // التحقق من وجود عنصر في المفضلة
  bool isFavorite(String id) {
    if (!_isInitialized) {
      // تحميل صامت إذا لم يتم التهيئة بعد
      _loadFavoritesQuietly();
    }
    return _favorites.any((item) => item.id == id);
  }

  // إضافة حدث سيرة للمفضلة
  Future<void> addSeerahEvent(SeerahEvent event) async {
    if (!isFavorite(event.id)) {
      final favoriteItem = FavoriteItem.fromSeerahEvent(event);
      _favorites.insert(0, favoriteItem); // إضافة في المقدمة
      notifyListeners();
      await _saveFavorites();
    }
  }

  // إضافة حديث للمفضلة
  Future<void> addHadith(Hadith hadith) async {
    if (!isFavorite(hadith.id)) {
      final favoriteItem = FavoriteItem.fromHadith(hadith);
      _favorites.insert(0, favoriteItem); // إضافة في المقدمة
      notifyListeners();
      await _saveFavorites();
    }
  }

  // إزالة من المفضلة
  Future<void> removeFavorite(String id) async {
    final initialLength = _favorites.length;
    _favorites.removeWhere((item) => item.id == id);

    if (_favorites.length != initialLength) {
      notifyListeners();
      await _saveFavorites();
    }
  }

  // تبديل حالة المفضلة
  Future<void> toggleSeerahFavorite(SeerahEvent event) async {
    if (isFavorite(event.id)) {
      await removeFavorite(event.id);
    } else {
      await addSeerahEvent(event);
    }
  }

  // تبديل حالة المفضلة للحديث
  Future<void> toggleHadithFavorite(Hadith hadith) async {
    // التأكد من التهيئة
    if (!_isInitialized) {
      await _loadFavoritesQuietly();
    }

    if (isFavorite(hadith.id)) {
      await removeFavorite(hadith.id);
    } else {
      await addHadith(hadith);
    }

    // إشعار فوري للتحديث
    notifyListeners();
  }

  // مسح جميع المفضلة
  Future<void> clearAllFavorites() async {
    _favorites.clear();
    _isInitialized = false; // إعادة تعيين حالة التهيئة
    notifyListeners();
    await _saveFavorites();
  }

  // مسح مفضلة نوع معين
  Future<void> clearFavoritesByType(FavoriteType type) async {
    final initialLength = _favorites.length;
    _favorites.removeWhere((item) => item.type == type);

    if (_favorites.length != initialLength) {
      notifyListeners();
      await _saveFavorites();
    }
  }

  // البحث في المفضلة
  List<FavoriteItem> searchFavorites(String query) {
    if (query.isEmpty) return _favorites;

    final lowerQuery = query.toLowerCase();
    return _favorites.where((item) {
      return item.title.toLowerCase().contains(lowerQuery) ||
             item.subtitle.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  // الحصول على المفضلة حسب النوع
  List<FavoriteItem> getFavoritesByType(FavoriteType type) {
    return _favorites.where((item) => item.type == type).toList();
  }

  // الحصول على أحدث المفضلة
  List<FavoriteItem> getRecentFavorites({int limit = 5}) {
    final sortedFavorites = List<FavoriteItem>.from(_favorites);
    sortedFavorites.sort((a, b) => b.addedAt.compareTo(a.addedAt));
    return sortedFavorites.take(limit).toList();
  }

  // إحصائيات المفضلة
  Map<String, int> get statistics => {
    'total': totalCount,
    'seerah': seerahCount,
    'hadith': hadithCount,
  };

  // التحقق من وجود مفضلة
  bool get hasFavorites => _favorites.isNotEmpty;
  bool get hasSeerahFavorites => seerahFavorites.isNotEmpty;
  bool get hasHadithFavorites => hadithFavorites.isNotEmpty;

  // الحصول على رسالة حالة المفضلة
  String get statusMessage {
    if (_isLoading) return 'جاري تحميل المفضلة...';
    if (_favorites.isEmpty) return 'لا توجد عناصر مفضلة';
    return 'لديك ${_favorites.length} عنصر مفضل';
  }

  // تصدير المفضلة كنص
  String exportFavoritesAsText() {
    if (_favorites.isEmpty) return 'لا توجد عناصر مفضلة للتصدير';

    final buffer = StringBuffer();
    buffer.writeln('المفضلة - تطبيق السيرة النبوية');
    buffer.writeln('تاريخ التصدير: ${DateTime.now().toString().split('.')[0]}');
    buffer.writeln('عدد العناصر: ${_favorites.length}');
    buffer.writeln('=' * 50);

    for (final item in _favorites) {
      buffer.writeln();
      buffer.writeln('النوع: ${item.type == FavoriteType.seerah ? 'السيرة النبوية' : 'الأحاديث'}');
      buffer.writeln('العنوان: ${item.title}');
      buffer.writeln('الوصف: ${item.subtitle}');
      buffer.writeln('تاريخ الإضافة: ${item.addedAt.toString().split('.')[0]}');
      buffer.writeln('-' * 30);
    }

    return buffer.toString();
  }
}
