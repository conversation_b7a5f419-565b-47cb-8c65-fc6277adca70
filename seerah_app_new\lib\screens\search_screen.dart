import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/seerah_provider.dart';
import '../providers/hadith_provider.dart';
import '../providers/theme_provider.dart';
import '../providers/favorites_provider.dart';
import '../models/seerah_event.dart';
import '../models/hadith.dart';
import '../theme/app_theme.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _performSearch(String query) {
    setState(() {
      _searchQuery = query;
      _isSearching = query.isNotEmpty;
    });

    if (query.isNotEmpty) {
      final seerahProvider = Provider.of<SeerahProvider>(context, listen: false);
      final hadithProvider = Provider.of<HadithProvider>(context, listen: false);

      seerahProvider.updateSearch(query);
      hadithProvider.updateSearch(query);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<ThemeProvider, FavoritesProvider, SeerahProvider>(
      builder: (context, themeProvider, favoritesProvider, seerahProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              'البحث',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: themeProvider.isDarkMode ? AppTheme.onPrimaryDark : AppTheme.onPrimaryLight,
              ),
            ),
            backgroundColor: themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight,
            centerTitle: true,
            elevation: 0,
            iconTheme: IconThemeData(
              color: themeProvider.isDarkMode ? AppTheme.onPrimaryDark : AppTheme.onPrimaryLight,
            ),
            bottom: TabBar(
              controller: _tabController,
              indicatorColor: themeProvider.isDarkMode ? AppTheme.onPrimaryDark : AppTheme.onPrimaryLight,
              labelColor: themeProvider.isDarkMode ? AppTheme.onPrimaryDark : AppTheme.onPrimaryLight,
              unselectedLabelColor: (themeProvider.isDarkMode ? AppTheme.onPrimaryDark : AppTheme.onPrimaryLight).withValues(alpha: 0.7),
              labelStyle: TextStyle(fontSize: (themeProvider.fontSize - 2).clamp(10.0, 20.0)),
              unselectedLabelStyle: TextStyle(fontSize: (themeProvider.fontSize - 2).clamp(10.0, 20.0)),
              tabs: const [
                Tab(
                  icon: Icon(Icons.book_rounded),
                  text: 'السيرة النبوية',
                ),
                Tab(
                  icon: Icon(Icons.format_quote_rounded),
                  text: 'الأحاديث',
                ),
              ],
            ),
          ),
          body: Column(
            children: [
              // شريط البحث
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: themeProvider.isDarkMode
                        ? [AppTheme.primaryDark, AppTheme.primaryDark.withValues(alpha: 0.8)]
                        : [AppTheme.primaryLight, AppTheme.primaryLight.withValues(alpha: 0.8)],
                  ),
                ),
                child: TextField(
                  controller: _searchController,
                  onChanged: _performSearch,
                  textDirection: TextDirection.rtl,
                  textAlign: TextAlign.right,
                  style: TextStyle(
                    color: themeProvider.isDarkMode ? Colors.black87 : Colors.black87,
                    fontSize: themeProvider.fontSize,
                    fontWeight: FontWeight.w500,
                  ),
                  decoration: InputDecoration(
                    hintText: 'ابحث في السيرة والأحاديث...',
                    hintStyle: TextStyle(
                      color: themeProvider.isDarkMode ? Colors.black54 : Colors.black54,
                      fontSize: themeProvider.fontSize,
                    ),
                    prefixIcon: Icon(
                      Icons.search,
                      color: themeProvider.isDarkMode ? Colors.black54 : Colors.black54,
                    ),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: Icon(
                              Icons.clear,
                              color: themeProvider.isDarkMode ? Colors.black54 : Colors.black54,
                            ),
                            onPressed: () {
                              _searchController.clear();
                              _performSearch('');
                            },
                          )
                        : null,
                    filled: true,
                    fillColor: themeProvider.isDarkMode ? Colors.white : Colors.white,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                      borderSide: BorderSide.none,
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 16,
                    ),
                  ),
                ),
              ),

              // نتائج البحث
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildSeerahResults(themeProvider, favoritesProvider),
                    _buildHadithResults(themeProvider, favoritesProvider),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSeerahResults(ThemeProvider themeProvider, FavoritesProvider favoritesProvider) {
    return Consumer<SeerahProvider>(
      builder: (context, seerahProvider, child) {
        if (!_isSearching) {
          return _buildEmptyState(
            themeProvider,
            icon: Icons.book_rounded,
            title: 'ابحث في السيرة النبوية',
            subtitle: 'اكتب في شريط البحث للعثور على الأحداث',
          );
        }

        final results = seerahProvider.searchEvents(_searchQuery);

        if (results.isEmpty) {
          return _buildNoResults(themeProvider, 'لم يتم العثور على أحداث تطابق البحث');
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: results.length,
          itemBuilder: (context, index) {
            final event = results[index];
            return _buildSeerahCard(event, themeProvider, favoritesProvider);
          },
        );
      },
    );
  }

  Widget _buildHadithResults(ThemeProvider themeProvider, FavoritesProvider favoritesProvider) {
    return Consumer<HadithProvider>(
      builder: (context, hadithProvider, child) {
        if (!_isSearching) {
          return _buildEmptyState(
            themeProvider,
            icon: Icons.format_quote_rounded,
            title: 'ابحث في الأحاديث النبوية',
            subtitle: 'اكتب في شريط البحث للعثور على الأحاديث',
          );
        }

        final results = hadithProvider.searchHadiths(_searchQuery);

        if (results.isEmpty) {
          return _buildNoResults(themeProvider, 'لم يتم العثور على أحاديث تطابق البحث');
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: results.length,
          itemBuilder: (context, index) {
            final hadith = results[index];
            return _buildHadithCard(hadith, themeProvider, favoritesProvider);
          },
        );
      },
    );
  }

  Widget _buildEmptyState(
    ThemeProvider themeProvider, {
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 80,
            color: (themeProvider.isDarkMode
                    ? AppTheme.onSurfaceDark
                    : AppTheme.onSurfaceLight)
                .withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: themeProvider.applyFontSize(
              TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: themeProvider.isDarkMode
                    ? AppTheme.onSurfaceDark
                    : AppTheme.onSurfaceLight,
              ),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: themeProvider.applyFontSize(
              TextStyle(
                fontSize: 16,
                color: (themeProvider.isDarkMode
                        ? AppTheme.onSurfaceDark
                        : AppTheme.onSurfaceLight)
                    .withValues(alpha: 0.7),
              ),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNoResults(ThemeProvider themeProvider, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off_rounded,
            size: 80,
            color: (themeProvider.isDarkMode
                    ? AppTheme.onSurfaceDark
                    : AppTheme.onSurfaceLight)
                .withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: themeProvider.applyFontSize(
              TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: themeProvider.isDarkMode
                    ? AppTheme.onSurfaceDark
                    : AppTheme.onSurfaceLight,
              ),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'جرب كلمات بحث مختلفة',
            style: themeProvider.applyFontSize(
              TextStyle(
                fontSize: 14,
                color: (themeProvider.isDarkMode
                        ? AppTheme.onSurfaceDark
                        : AppTheme.onSurfaceLight)
                    .withValues(alpha: 0.7),
              ),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSeerahCard(SeerahEvent event, ThemeProvider themeProvider, FavoritesProvider favoritesProvider) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: themeProvider.isDarkMode ? AppTheme.surfaceDark : AppTheme.surfaceLight,
          borderRadius: BorderRadius.circular(16),
          boxShadow: themeProvider.isDarkMode ? null : AppTheme.lightShadow,
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: (themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.event_rounded,
                    color: themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    event.title,
                    style: themeProvider.applyFontSize(
                      TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight,
                      ),
                    ),
                  ),
                ),
                // زر المفضلة
                IconButton(
                  onPressed: () {
                    favoritesProvider.toggleSeerahFavorite(event);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          favoritesProvider.isFavorite(event.id)
                              ? 'تم إضافة الحدث للمفضلة'
                              : 'تم إزالة الحدث من المفضلة',
                        ),
                        duration: const Duration(seconds: 2),
                      ),
                    );
                  },
                  icon: Icon(
                    favoritesProvider.isFavorite(event.id)
                        ? Icons.favorite
                        : Icons.favorite_border,
                    color: favoritesProvider.isFavorite(event.id)
                        ? Colors.red
                        : (themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight).withValues(alpha: 0.7),
                  ),
                  tooltip: favoritesProvider.isFavorite(event.id)
                      ? 'إزالة من المفضلة'
                      : 'إضافة للمفضلة',
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              event.subtitle,
              style: themeProvider.applyFontSize(
                TextStyle(
                  fontSize: 14,
                  color: (themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight).withValues(alpha: 0.8),
                ),
              ),
            ),
            if (event.description.isNotEmpty) ...[
              const SizedBox(height: 12),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: themeProvider.isDarkMode ? AppTheme.backgroundDark : AppTheme.backgroundLight,
                  borderRadius: BorderRadius.circular(12),
                  border: themeProvider.isHighContrast
                      ? Border.all(color: themeProvider.isDarkMode ? Colors.white54 : Colors.black54)
                      : null,
                ),
                child: Text(
                  event.description,
                  style: themeProvider.applyFontSize(
                    TextStyle(
                      fontSize: 13,
                      color: themeProvider.isDarkMode ? AppTheme.onBackgroundDark : AppTheme.onBackgroundLight,
                      height: 1.4,
                    ),
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
            const SizedBox(height: 12),
            Row(
              children: [
                if (event.date.isNotEmpty) ...[
                  Icon(
                    Icons.calendar_today,
                    size: 14,
                    color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark.withValues(alpha: 0.7) : AppTheme.onSurfaceLight.withValues(alpha: 0.7),
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      event.date,
                      style: themeProvider.applyFontSize(
                        TextStyle(
                          fontSize: 12,
                          color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark.withValues(alpha: 0.7) : AppTheme.onSurfaceLight.withValues(alpha: 0.7),
                        ),
                      ),
                    ),
                  ),
                ],
                if (event.date.isNotEmpty && event.location.isNotEmpty)
                  const SizedBox(width: 8),
                if (event.location.isNotEmpty) ...[
                  Icon(
                    Icons.location_on,
                    size: 14,
                    color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark.withValues(alpha: 0.7) : AppTheme.onSurfaceLight.withValues(alpha: 0.7),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    event.location,
                    style: themeProvider.applyFontSize(
                      TextStyle(
                        fontSize: 12,
                        color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark.withValues(alpha: 0.7) : AppTheme.onSurfaceLight.withValues(alpha: 0.7),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHadithCard(Hadith hadith, ThemeProvider themeProvider, FavoritesProvider favoritesProvider) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: themeProvider.isDarkMode ? AppTheme.surfaceDark : AppTheme.surfaceLight,
          borderRadius: BorderRadius.circular(16),
          boxShadow: themeProvider.isDarkMode ? null : AppTheme.lightShadow,
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: (themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.format_quote_rounded,
                    color: themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    hadith.category,
                    style: themeProvider.applyFontSize(
                      TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight,
                      ),
                    ),
                  ),
                ),
                // زر المفضلة
                IconButton(
                  onPressed: () {
                    favoritesProvider.toggleHadithFavorite(hadith);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          favoritesProvider.isFavorite(hadith.id)
                              ? 'تم إضافة الحديث للمفضلة'
                              : 'تم إزالة الحديث من المفضلة',
                        ),
                        duration: const Duration(seconds: 2),
                      ),
                    );
                  },
                  icon: Icon(
                    favoritesProvider.isFavorite(hadith.id)
                        ? Icons.favorite
                        : Icons.favorite_border,
                    color: favoritesProvider.isFavorite(hadith.id)
                        ? Colors.red
                        : (themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight).withValues(alpha: 0.7),
                  ),
                  tooltip: favoritesProvider.isFavorite(hadith.id)
                      ? 'إزالة من المفضلة'
                      : 'إضافة للمفضلة',
                ),
                if (hadith.isAuthentic)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                    ),
                    child: Text(
                      'صحيح',
                      style: themeProvider.applyFontSize(
                        const TextStyle(
                          fontSize: 10,
                          color: Colors.green,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: themeProvider.isDarkMode ? AppTheme.backgroundDark : AppTheme.backgroundLight,
                borderRadius: BorderRadius.circular(12),
                border: themeProvider.isHighContrast
                    ? Border.all(color: themeProvider.isDarkMode ? Colors.white54 : Colors.black54)
                    : null,
              ),
              child: Text(
                hadith.arabicText,
                style: themeProvider.applyFontSize(
                  TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: themeProvider.isDarkMode ? AppTheme.onBackgroundDark : AppTheme.onBackgroundLight,
                    height: 1.6,
                  ),
                ),
                textAlign: TextAlign.right,
                textDirection: TextDirection.rtl,
                maxLines: 4,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                if (hadith.narrator.isNotEmpty) ...[
                  Icon(
                    Icons.person,
                    size: 16,
                    color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark.withValues(alpha: 0.7) : AppTheme.onSurfaceLight.withValues(alpha: 0.7),
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      hadith.narrator,
                      style: themeProvider.applyFontSize(
                        TextStyle(
                          fontSize: 12,
                          color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark.withValues(alpha: 0.8) : AppTheme.onSurfaceLight.withValues(alpha: 0.8),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],
                if (hadith.narrator.isNotEmpty && hadith.source.isNotEmpty)
                  const SizedBox(width: 12),
                if (hadith.source.isNotEmpty) ...[
                  Icon(
                    Icons.book,
                    size: 16,
                    color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark.withValues(alpha: 0.7) : AppTheme.onSurfaceLight.withValues(alpha: 0.7),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    hadith.source,
                    style: themeProvider.applyFontSize(
                      TextStyle(
                        fontSize: 12,
                        color: themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }
}
