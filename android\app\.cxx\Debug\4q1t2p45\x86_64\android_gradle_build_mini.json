{"buildFiles": ["D:\\def\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\AndroidstudioSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\uuu\\test1\\android\\app\\.cxx\\Debug\\4q1t2p45\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\AndroidstudioSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\uuu\\test1\\android\\app\\.cxx\\Debug\\4q1t2p45\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}