import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/hadith_provider.dart';
import '../models/hadith.dart';
import 'filter_screen.dart';

class HadithScreen extends StatelessWidget {
  const HadithScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'الأحاديث النبوية',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFFFF9800),
        centerTitle: true,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const FilterScreen(type: 'hadith'),
                ),
              );
            },
            icon: const Icon(
              Icons.tune_rounded,
              color: Colors.white,
            ),
            tooltip: 'فلترة النتائج',
          ),
        ],
      ),
      body: Consumer<HadithProvider>(
        builder: (context, hadithProvider, child) {
          return Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xFFFF9800),
                Color(0xFFFFB74D),
              ],
            ),
          ),
          child: Column(
            children: [
              // Header Section
              Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    const SizedBox(height: 20),

                    // أيقونة القسم
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const Icon(
                        Icons.format_quote_rounded,
                        size: 80,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 20),

                    // عنوان القسم
                    const Text(
                      'الأحاديث النبوية الشريفة',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 10),

                    // وصف القسم مع الإحصائيات
                    Text(
                      'مجموعة من ${hadithProvider.hadiths.length} حديث شريف مختار',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              // Content Section
              Expanded(
                child: hadithProvider.isLoading
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(color: Colors.white),
                            SizedBox(height: 16),
                            Text(
                              'جاري تحميل الأحاديث...',
                              style: TextStyle(color: Colors.white),
                            ),
                          ],
                        ),
                      )
                    : hadithProvider.hadiths.isEmpty
                        ? const Center(
                            child: Text(
                              'لا توجد أحاديث متاحة حالياً',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                              ),
                            ),
                          )
                        : ListView.builder(
                            padding: const EdgeInsets.all(20),
                            itemCount: hadithProvider.hadiths.length + 1,
                            itemBuilder: (context, index) {
                              if (index == hadithProvider.hadiths.length) {
                                // إضافة كارت الإحصائيات في النهاية
                                return _buildStatisticsCard(hadithProvider);
                              }

                              final hadith = hadithProvider.hadiths[index];
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 16),
                                child: _buildHadithCard(hadith),
                              );
                            },
                          ),
              ),
            ],
          ),
        );
        },
      ),
    );
  }

  Widget _buildHadithCard(Hadith hadith) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: hadith.isAvailable ? Colors.white : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(16),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with category and number
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: hadith.isAvailable
                        ? const Color(0xFFFF9800).withValues(alpha: 0.1)
                        : Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.format_quote_rounded,
                    size: 24,
                    color: hadith.isAvailable
                        ? const Color(0xFFFF9800)
                        : Colors.grey.shade600,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        hadith.category,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: hadith.isAvailable
                              ? const Color(0xFFFF9800)
                              : Colors.grey.shade600,
                        ),
                      ),
                      Text(
                        'حديث رقم ${hadith.number}',
                        style: TextStyle(
                          fontSize: 12,
                          color: hadith.isAvailable
                              ? Colors.grey.shade600
                              : Colors.grey.shade500,
                        ),
                      ),
                    ],
                  ),
                ),
                if (hadith.isAuthentic)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'صحيح',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // Arabic text
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: hadith.isAvailable
                    ? const Color(0xFFF5F5F5)
                    : Colors.grey.shade200,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                hadith.arabicText,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: hadith.isAvailable
                      ? const Color(0xFF424242)
                      : Colors.grey.shade600,
                  height: 1.8,
                ),
                textAlign: TextAlign.right,
                textDirection: TextDirection.rtl,
              ),
            ),

            if (hadith.translation.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                'المعنى: ${hadith.translation}',
                style: TextStyle(
                  fontSize: 14,
                  color: hadith.isAvailable
                      ? const Color(0xFF666666)
                      : Colors.grey.shade500,
                  height: 1.4,
                ),
              ),
            ],

            // Footer with narrator and source
            if (hadith.narrator.isNotEmpty || hadith.source.isNotEmpty) ...[
              const SizedBox(height: 12),
              Row(
                children: [
                  if (hadith.narrator.isNotEmpty) ...[
                    Icon(
                      Icons.person,
                      size: 14,
                      color: Colors.grey.shade600,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        hadith.narrator,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ),
                  ],
                  if (hadith.source.isNotEmpty) ...[
                    const SizedBox(width: 8),
                    Icon(
                      Icons.book,
                      size: 14,
                      color: Colors.grey.shade600,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      hadith.source,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsCard(HadithProvider provider) {
    final categoryCounts = provider.categoryCounts;

    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            const Icon(
              Icons.analytics_rounded,
              size: 40,
              color: Color(0xFFFF9800),
            ),
            const SizedBox(height: 12),
            const Text(
              'إحصائيات الأحاديث النبوية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF424242),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ...categoryCounts.entries.map((entry) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    entry.key,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF424242),
                    ),
                  ),
                  Text(
                    '${entry.value} أحاديث',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFFFF9800),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }
}
