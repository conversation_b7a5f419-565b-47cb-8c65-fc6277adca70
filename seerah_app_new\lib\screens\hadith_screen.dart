import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/hadith_provider.dart';
import '../providers/favorites_provider.dart';
import '../providers/theme_provider.dart';
import '../models/hadith.dart';
import '../theme/app_theme.dart';
import 'filter_screen.dart';

class HadithScreen extends StatefulWidget {
  const HadithScreen({super.key});

  @override
  State<HadithScreen> createState() => _HadithScreenState();
}

class _HadithScreenState extends State<HadithScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<HadithProvider, FavoritesProvider, ThemeProvider>(
      builder: (context, hadithProvider, favoritesProvider, themeProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              'الأحاديث النبوية',
              style: themeProvider.applyFontSize(
                Theme.of(context).textTheme.titleLarge!,
              ),
            ),
            backgroundColor: themeProvider.isDarkMode
                ? AppTheme.primaryDark
                : AppTheme.primaryLight,
            foregroundColor: themeProvider.isDarkMode
                ? AppTheme.onPrimaryDark
                : AppTheme.onPrimaryLight,
            centerTitle: true,
            elevation: 0,
            actions: [
              IconButton(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const FilterScreen(type: 'hadith'),
                    ),
                  );
                },
                icon: const Icon(Icons.tune_rounded),
                tooltip: 'فلترة النتائج',
              ),
            ],
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: themeProvider.isDarkMode
                    ? [AppTheme.primaryDark, AppTheme.primaryVariantDark]
                    : [AppTheme.primaryLight, AppTheme.primaryVariantLight],
              ),
            ),
            child: Column(
              children: [
                // Header Section
                Container(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      const SizedBox(height: 20),

                      // أيقونة القسم
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Icon(
                          Icons.format_quote_rounded,
                          size: 80,
                          color: themeProvider.isDarkMode
                              ? AppTheme.onPrimaryDark
                              : AppTheme.onPrimaryLight,
                        ),
                      ),
                      const SizedBox(height: 20),

                      // عنوان القسم
                      Text(
                        'الأحاديث النبوية الشريفة',
                        style: themeProvider.applyFontSize(
                          TextStyle(
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                            color: themeProvider.isDarkMode
                                ? AppTheme.onPrimaryDark
                                : AppTheme.onPrimaryLight,
                          ),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 10),

                      // وصف القسم مع الإحصائيات
                      Text(
                        'مجموعة من ${hadithProvider.hadiths.length} حديث شريف مختار',
                        style: themeProvider.applyFontSize(
                          TextStyle(
                            fontSize: 16,
                            color: (themeProvider.isDarkMode
                                    ? AppTheme.onPrimaryDark
                                    : AppTheme.onPrimaryLight)
                                .withValues(alpha: 0.7),
                          ),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 20),

                      // شريط البحث
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(25),
                        ),
                        child: TextField(
                          controller: _searchController,
                          textDirection: TextDirection.rtl,
                          style: TextStyle(
                            color: themeProvider.isDarkMode
                                ? AppTheme.onPrimaryDark
                                : AppTheme.onPrimaryLight,
                            fontSize: themeProvider.fontSize,
                          ),
                          decoration: InputDecoration(
                            hintText: 'ابحث في الأحاديث...',
                            hintStyle: TextStyle(
                              color: (themeProvider.isDarkMode
                                      ? AppTheme.onPrimaryDark
                                      : AppTheme.onPrimaryLight)
                                  .withValues(alpha: 0.7),
                              fontSize: themeProvider.fontSize,
                            ),
                            prefixIcon: Icon(
                              Icons.search,
                              color: themeProvider.isDarkMode
                                  ? AppTheme.onPrimaryDark
                                  : AppTheme.onPrimaryLight,
                            ),
                            suffixIcon: _searchQuery.isNotEmpty
                                ? IconButton(
                                    onPressed: () {
                                      _searchController.clear();
                                      setState(() {
                                        _searchQuery = '';
                                      });
                                    },
                                    icon: Icon(
                                      Icons.clear,
                                      color: themeProvider.isDarkMode
                                          ? AppTheme.onPrimaryDark
                                          : AppTheme.onPrimaryLight,
                                    ),
                                  )
                                : null,
                            border: InputBorder.none,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 15,
                            ),
                          ),
                          onChanged: (value) {
                            setState(() {
                              _searchQuery = value;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ),

                // Content Section
                Expanded(
                  child: hadithProvider.isLoading
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CircularProgressIndicator(
                                color: themeProvider.isDarkMode
                                    ? AppTheme.onPrimaryDark
                                    : AppTheme.onPrimaryLight,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'جاري تحميل الأحاديث...',
                                style: TextStyle(
                                  color: themeProvider.isDarkMode
                                      ? AppTheme.onPrimaryDark
                                      : AppTheme.onPrimaryLight,
                                ),
                              ),
                            ],
                          ),
                        )
                      : _buildHadithsList(hadithProvider, favoritesProvider, themeProvider),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHadithsList(HadithProvider hadithProvider, FavoritesProvider favoritesProvider, ThemeProvider themeProvider) {
    // تطبيق البحث
    final filteredHadiths = _searchQuery.isEmpty
        ? hadithProvider.hadiths
        : hadithProvider.hadiths.where((hadith) {
            final query = _searchQuery.toLowerCase();
            return hadith.arabicText.toLowerCase().contains(query) ||
                   hadith.translation.toLowerCase().contains(query) ||
                   hadith.category.toLowerCase().contains(query) ||
                   hadith.narrator.toLowerCase().contains(query);
          }).toList();

    if (filteredHadiths.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _searchQuery.isEmpty ? Icons.format_quote_rounded : Icons.search_off,
              size: 80,
              color: (themeProvider.isDarkMode
                      ? AppTheme.onPrimaryDark
                      : AppTheme.onPrimaryLight)
                  .withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isEmpty
                  ? 'لا توجد أحاديث متاحة حالياً'
                  : 'لا توجد نتائج للبحث',
              style: TextStyle(
                color: themeProvider.isDarkMode
                    ? AppTheme.onPrimaryDark
                    : AppTheme.onPrimaryLight,
                fontSize: 18,
              ),
            ),
            if (_searchQuery.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'جرب كلمات بحث مختلفة',
                style: TextStyle(
                  color: (themeProvider.isDarkMode
                          ? AppTheme.onPrimaryDark
                          : AppTheme.onPrimaryLight)
                      .withValues(alpha: 0.7),
                  fontSize: 14,
                ),
              ),
            ],
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: filteredHadiths.length + (_searchQuery.isEmpty ? 1 : 0),
      itemBuilder: (context, index) {
        if (_searchQuery.isEmpty && index == filteredHadiths.length) {
          // إضافة كارت الإحصائيات في النهاية فقط عند عدم البحث
          return _buildStatisticsCard(hadithProvider, themeProvider);
        }

        final hadith = filteredHadiths[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildHadithCard(hadith, favoritesProvider, themeProvider, context),
        );
      },
    );
  }

  Widget _buildHadithCard(Hadith hadith, FavoritesProvider favoritesProvider, ThemeProvider themeProvider, BuildContext context) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: hadith.isAvailable
              ? (themeProvider.isDarkMode ? AppTheme.surfaceDark : AppTheme.surfaceLight)
              : (themeProvider.isDarkMode ? Colors.grey.shade800 : Colors.grey.shade100),
          borderRadius: BorderRadius.circular(16),
          boxShadow: themeProvider.isDarkMode ? null : AppTheme.lightShadow,
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with category and number
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: hadith.isAvailable
                        ? (themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight).withValues(alpha: 0.1)
                        : Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.format_quote_rounded,
                    size: 24,
                    color: hadith.isAvailable
                        ? (themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight)
                        : Colors.grey.shade600,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        hadith.category,
                        style: themeProvider.applyFontSize(
                          TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: hadith.isAvailable
                                ? (themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight)
                                : Colors.grey.shade600,
                          ),
                        ),
                      ),
                      Text(
                        'حديث رقم ${hadith.number}',
                        style: themeProvider.applyFontSize(
                          TextStyle(
                            fontSize: 12,
                            color: hadith.isAvailable
                                ? (themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight).withValues(alpha: 0.7)
                                : Colors.grey.shade500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // زر المفضلة
                    IconButton(
                      onPressed: () {
                        favoritesProvider.toggleHadithFavorite(hadith);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              favoritesProvider.isFavorite(hadith.id)
                                  ? 'تم إضافة الحديث للمفضلة'
                                  : 'تم إزالة الحديث من المفضلة',
                            ),
                            duration: const Duration(seconds: 2),
                          ),
                        );
                      },
                      icon: Icon(
                        favoritesProvider.isFavorite(hadith.id)
                            ? Icons.favorite
                            : Icons.favorite_border,
                        color: favoritesProvider.isFavorite(hadith.id)
                            ? Colors.red
                            : (themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight).withValues(alpha: 0.7),
                      ),
                      tooltip: favoritesProvider.isFavorite(hadith.id)
                          ? 'إزالة من المفضلة'
                          : 'إضافة للمفضلة',
                    ),
                    // علامة الصحة
                    if (hadith.isAuthentic)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppTheme.success.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'صحيح',
                          style: themeProvider.applyFontSize(
                            const TextStyle(
                              fontSize: 10,
                              color: AppTheme.success,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Arabic text
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: hadith.isAvailable
                    ? (themeProvider.isDarkMode ? AppTheme.backgroundDark : AppTheme.backgroundLight)
                    : (themeProvider.isDarkMode ? Colors.grey.shade700 : Colors.grey.shade200),
                borderRadius: BorderRadius.circular(12),
                border: themeProvider.isHighContrast
                    ? Border.all(color: themeProvider.isDarkMode ? Colors.white54 : Colors.black54)
                    : null,
              ),
              child: Text(
                hadith.arabicText,
                style: themeProvider.applyFontSize(
                  TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: hadith.isAvailable
                        ? (themeProvider.isDarkMode ? AppTheme.onBackgroundDark : AppTheme.onBackgroundLight)
                        : (themeProvider.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600),
                    height: 1.8,
                  ),
                ),
                textAlign: TextAlign.right,
                textDirection: TextDirection.rtl,
              ),
            ),

            if (hadith.translation.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                'المعنى: ${hadith.translation}',
                style: themeProvider.applyFontSize(
                  TextStyle(
                    fontSize: 14,
                    color: hadith.isAvailable
                        ? (themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight).withValues(alpha: 0.8)
                        : (themeProvider.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade500),
                    height: 1.4,
                  ),
                ),
              ),
            ],

            // Footer with narrator and source
            if (hadith.narrator.isNotEmpty || hadith.source.isNotEmpty) ...[
              const SizedBox(height: 12),
              Row(
                children: [
                  if (hadith.narrator.isNotEmpty) ...[
                    Icon(
                      Icons.person,
                      size: 14,
                      color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark.withValues(alpha: 0.7) : AppTheme.onSurfaceLight.withValues(alpha: 0.7),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        hadith.narrator,
                        style: themeProvider.applyFontSize(
                          TextStyle(
                            fontSize: 12,
                            color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark.withValues(alpha: 0.7) : AppTheme.onSurfaceLight.withValues(alpha: 0.7),
                          ),
                        ),
                      ),
                    ),
                  ],
                  if (hadith.source.isNotEmpty) ...[
                    const SizedBox(width: 8),
                    Icon(
                      Icons.book,
                      size: 14,
                      color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark.withValues(alpha: 0.7) : AppTheme.onSurfaceLight.withValues(alpha: 0.7),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      hadith.source,
                      style: themeProvider.applyFontSize(
                        TextStyle(
                          fontSize: 12,
                          color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark.withValues(alpha: 0.7) : AppTheme.onSurfaceLight.withValues(alpha: 0.7),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsCard(HadithProvider provider, ThemeProvider themeProvider) {
    final categoryCounts = provider.categoryCounts;

    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: themeProvider.isDarkMode ? AppTheme.surfaceDark : AppTheme.surfaceLight,
          borderRadius: BorderRadius.circular(16),
          boxShadow: themeProvider.isDarkMode ? null : AppTheme.lightShadow,
          border: themeProvider.isHighContrast
              ? Border.all(color: themeProvider.isDarkMode ? Colors.white54 : Colors.black54)
              : null,
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(
              Icons.analytics_rounded,
              size: 40,
              color: themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight,
            ),
            const SizedBox(height: 12),
            Text(
              'إحصائيات الأحاديث النبوية',
              style: themeProvider.applyFontSize(
                TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight,
                ),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ...categoryCounts.entries.map((entry) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    entry.key,
                    style: themeProvider.applyFontSize(
                      TextStyle(
                        fontSize: 14,
                        color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight,
                      ),
                    ),
                  ),
                  Text(
                    '${entry.value} أحاديث',
                    style: themeProvider.applyFontSize(
                      TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight,
                      ),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }
}
