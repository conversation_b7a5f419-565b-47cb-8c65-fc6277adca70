import 'package:flutter/material.dart';

class HadithScreen extends StatelessWidget {
  const HadithScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFFFF9800),
            Color(0xFFFFB74D),
          ],
        ),
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            const SizedBox(height: 20),

            // أيقونة القسم
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.format_quote_rounded,
                size: 80,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 20),

            // عنوان القسم
            const Text(
              'الأحاديث النبوية الشريفة',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 10),

            // وصف القسم
            const Text(
              'مجموعة من الأحاديث الشريفة المختارة',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 40),

            // كروت الأحاديث (عينة)
            _buildHadithCard(
              hadith: 'إنما الأعمال بالنيات',
              category: 'أحاديث العقيدة',
              icon: Icons.favorite_rounded,
              isComingSoon: false,
            ),
            const SizedBox(height: 16),

            _buildHadithCard(
              hadith: 'المسلم من سلم المسلمون من لسانه ويده',
              category: 'أحاديث الأخلاق',
              icon: Icons.handshake_rounded,
              isComingSoon: false,
            ),
            const SizedBox(height: 16),

            _buildHadithCard(
              hadith: 'لا يؤمن أحدكم حتى يحب لأخيه ما يحب لنفسه',
              category: 'أحاديث المعاملات',
              icon: Icons.people_rounded,
              isComingSoon: false,
            ),
            const SizedBox(height: 16),

            _buildHadithCard(
              hadith: 'المزيد من الأحاديث',
              category: 'سيتم إضافة 47 حديث آخر قريباً',
              icon: Icons.more_horiz_rounded,
              isComingSoon: true,
            ),
            const SizedBox(height: 40),

            // تصنيفات الأحاديث
            Card(
              elevation: 8,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    const Icon(
                      Icons.category_rounded,
                      size: 40,
                      color: Color(0xFFFF9800),
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      'تصنيفات الأحاديث',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF424242),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    _buildCategoryRow('أحاديث العقيدة', '10 أحاديث'),
                    _buildCategoryRow('أحاديث العبادة', '15 حديث'),
                    _buildCategoryRow('أحاديث الأخلاق', '15 حديث'),
                    _buildCategoryRow('أحاديث المعاملات', '10 أحاديث'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHadithCard({
    required String hadith,
    required String category,
    required IconData icon,
    required bool isComingSoon,
  }) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: isComingSoon ? Colors.grey.shade100 : Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: isComingSoon
                        ? Colors.grey.shade300
                        : const Color(0xFFFF9800).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    size: 24,
                    color: isComingSoon
                        ? Colors.grey.shade600
                        : const Color(0xFFFF9800),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    category,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: isComingSoon
                          ? Colors.grey.shade600
                          : const Color(0xFFFF9800),
                    ),
                  ),
                ),
                Icon(
                  isComingSoon ? Icons.schedule : Icons.arrow_forward_ios,
                  color: isComingSoon
                      ? Colors.grey.shade400
                      : const Color(0xFFFF9800),
                  size: 20,
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              hadith,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: isComingSoon
                    ? Colors.grey.shade600
                    : const Color(0xFF424242),
                height: 1.5,
              ),
              textAlign: TextAlign.right,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryRow(String title, String count) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF424242),
            ),
          ),
          Text(
            count,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Color(0xFFFF9800),
            ),
          ),
        ],
      ),
    );
  }
}
