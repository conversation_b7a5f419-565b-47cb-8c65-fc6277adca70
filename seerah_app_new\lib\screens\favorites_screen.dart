import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/favorites_provider.dart';
import '../providers/theme_provider.dart';
import '../theme/app_theme.dart';

class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // تحميل المفضلة عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final favoritesProvider = Provider.of<FavoritesProvider>(context, listen: false);
      if (favoritesProvider.favorites.isEmpty) {
        favoritesProvider.loadFavorites();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<FavoritesProvider, ThemeProvider>(
      builder: (context, favoritesProvider, themeProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              'المفضلة',
              style: themeProvider.applyFontSize(
                Theme.of(context).textTheme.titleLarge!,
              ),
            ),
            backgroundColor: themeProvider.isDarkMode
                ? AppTheme.primaryDark
                : AppTheme.primaryLight,
            foregroundColor: themeProvider.isDarkMode
                ? AppTheme.onPrimaryDark
                : AppTheme.onPrimaryLight,
            centerTitle: true,
            elevation: 0,
            actions: [
              if (favoritesProvider.hasFavorites)
                PopupMenuButton<String>(
                  onSelected: (value) => _handleMenuAction(value, favoritesProvider),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'clear_all',
                      child: Row(
                        children: [
                          Icon(Icons.clear_all, color: Colors.red),
                          SizedBox(width: 8),
                          Text('مسح الكل'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'export',
                      child: Row(
                        children: [
                          Icon(Icons.share),
                          SizedBox(width: 8),
                          Text('تصدير'),
                        ],
                      ),
                    ),
                  ],
                ),
            ],
            bottom: TabBar(
              controller: _tabController,
              indicatorColor: themeProvider.isDarkMode
                  ? AppTheme.onPrimaryDark
                  : AppTheme.onPrimaryLight,
              labelColor: themeProvider.isDarkMode
                  ? AppTheme.onPrimaryDark
                  : AppTheme.onPrimaryLight,
              unselectedLabelColor: themeProvider.isDarkMode
                  ? AppTheme.onPrimaryDark.withValues(alpha: 0.7)
                  : AppTheme.onPrimaryLight.withValues(alpha: 0.7),
              tabs: [
                Tab(
                  icon: const Icon(Icons.all_inclusive),
                  text: 'الكل (${favoritesProvider.totalCount})',
                ),
                Tab(
                  icon: const Icon(Icons.book_rounded),
                  text: 'السيرة (${favoritesProvider.seerahCount})',
                ),
                Tab(
                  icon: const Icon(Icons.format_quote_rounded),
                  text: 'الأحاديث (${favoritesProvider.hadithCount})',
                ),
              ],
            ),
          ),
          body: Column(
            children: [
              // شريط البحث
              if (favoritesProvider.hasFavorites)
                Container(
                  padding: const EdgeInsets.all(AppTheme.mediumPadding),
                  decoration: BoxDecoration(
                    gradient: AppTheme.getPrimaryGradient(themeProvider.isDarkMode),
                  ),
                  child: TextField(
                    controller: _searchController,
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                    textDirection: TextDirection.rtl,
                    style: themeProvider.applyFontSize(
                      const TextStyle(color: Colors.white),
                    ),
                    decoration: InputDecoration(
                      hintText: 'ابحث في المفضلة...',
                      hintStyle: themeProvider.applyFontSize(
                        const TextStyle(color: Colors.white70),
                      ),
                      prefixIcon: const Icon(Icons.search, color: Colors.white70),
                      suffixIcon: _searchQuery.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear, color: Colors.white70),
                              onPressed: () {
                                _searchController.clear();
                                setState(() {
                                  _searchQuery = '';
                                });
                              },
                            )
                          : null,
                      filled: true,
                      fillColor: Colors.white.withValues(alpha: 0.2),
                      border: OutlineInputBorder(
                        borderRadius: AppTheme.extraLargeRadius,
                        borderSide: BorderSide.none,
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.mediumPadding,
                        vertical: AppTheme.smallPadding,
                      ),
                    ),
                  ),
                ),

              // محتوى التبويبات
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildAllFavorites(favoritesProvider, themeProvider),
                    _buildSeerahFavorites(favoritesProvider, themeProvider),
                    _buildHadithFavorites(favoritesProvider, themeProvider),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAllFavorites(FavoritesProvider provider, ThemeProvider themeProvider) {
    if (provider.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    final favorites = _searchQuery.isEmpty
        ? provider.favorites
        : provider.searchFavorites(_searchQuery);

    if (favorites.isEmpty) {
      return _buildEmptyState(
        icon: Icons.favorite_border,
        title: _searchQuery.isEmpty ? 'لا توجد عناصر مفضلة' : 'لا توجد نتائج',
        subtitle: _searchQuery.isEmpty
            ? 'ابدأ بإضافة عناصر للمفضلة من السيرة والأحاديث'
            : 'جرب كلمات بحث مختلفة',
        themeProvider: themeProvider,
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppTheme.mediumPadding),
      itemCount: favorites.length,
      itemBuilder: (context, index) {
        final favorite = favorites[index];
        return _buildFavoriteCard(favorite, provider, themeProvider);
      },
    );
  }

  Widget _buildSeerahFavorites(FavoritesProvider provider, ThemeProvider themeProvider) {
    if (provider.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    final favorites = _searchQuery.isEmpty
        ? provider.seerahFavorites
        : provider.searchFavorites(_searchQuery)
            .where((item) => item.type == FavoriteType.seerah)
            .toList();

    if (favorites.isEmpty) {
      return _buildEmptyState(
        icon: Icons.book_rounded,
        title: 'لا توجد أحداث سيرة مفضلة',
        subtitle: 'أضف أحداث من السيرة النبوية للمفضلة',
        themeProvider: themeProvider,
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppTheme.mediumPadding),
      itemCount: favorites.length,
      itemBuilder: (context, index) {
        final favorite = favorites[index];
        return _buildFavoriteCard(favorite, provider, themeProvider);
      },
    );
  }

  Widget _buildHadithFavorites(FavoritesProvider provider, ThemeProvider themeProvider) {
    if (provider.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    final favorites = _searchQuery.isEmpty
        ? provider.hadithFavorites
        : provider.searchFavorites(_searchQuery)
            .where((item) => item.type == FavoriteType.hadith)
            .toList();

    if (favorites.isEmpty) {
      return _buildEmptyState(
        icon: Icons.format_quote_rounded,
        title: 'لا توجد أحاديث مفضلة',
        subtitle: 'أضف أحاديث نبوية للمفضلة',
        themeProvider: themeProvider,
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppTheme.mediumPadding),
      itemCount: favorites.length,
      itemBuilder: (context, index) {
        final favorite = favorites[index];
        return _buildFavoriteCard(favorite, provider, themeProvider);
      },
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
    required ThemeProvider themeProvider,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: AppTheme.extraLargeIconSize * 2,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: AppTheme.mediumPadding),
          Text(
            title,
            style: themeProvider.applyFontSize(
              TextStyle(
                fontSize: AppTheme.headline5Size,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade600,
              ),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.smallPadding),
          Text(
            subtitle,
            style: themeProvider.applyFontSize(
              TextStyle(
                fontSize: AppTheme.bodyText2Size,
                color: Colors.grey.shade500,
              ),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFavoriteCard(
    FavoriteItem favorite,
    FavoritesProvider provider,
    ThemeProvider themeProvider,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppTheme.smallPadding),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: AppTheme.mediumRadius,
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.mediumPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppTheme.smallPadding),
                  decoration: BoxDecoration(
                    color: favorite.type == FavoriteType.seerah
                        ? AppTheme.success.withValues(alpha: 0.1)
                        : AppTheme.warning.withValues(alpha: 0.1),
                    borderRadius: AppTheme.smallRadius,
                  ),
                  child: Icon(
                    favorite.type == FavoriteType.seerah
                        ? Icons.book_rounded
                        : Icons.format_quote_rounded,
                    color: favorite.type == FavoriteType.seerah
                        ? AppTheme.success
                        : AppTheme.warning,
                    size: AppTheme.mediumIconSize,
                  ),
                ),
                const SizedBox(width: AppTheme.smallPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        favorite.title,
                        style: themeProvider.applyFontSize(
                          const TextStyle(
                            fontSize: AppTheme.headline6Size,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Text(
                        favorite.type == FavoriteType.seerah ? 'السيرة النبوية' : 'الأحاديث',
                        style: themeProvider.applyFontSize(
                          TextStyle(
                            fontSize: AppTheme.captionSize,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => _removeFavorite(favorite.id, provider),
                  icon: const Icon(
                    Icons.favorite,
                    color: Colors.red,
                  ),
                  tooltip: 'إزالة من المفضلة',
                ),
              ],
            ),
            const SizedBox(height: AppTheme.smallPadding),
            Text(
              favorite.subtitle,
              style: themeProvider.applyFontSize(
                const TextStyle(
                  fontSize: AppTheme.bodyText2Size,
                  height: 1.4,
                ),
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: AppTheme.smallPadding),
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: AppTheme.smallIconSize,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: AppTheme.extraSmallPadding),
                Text(
                  _formatDate(favorite.addedAt),
                  style: themeProvider.applyFontSize(
                    TextStyle(
                      fontSize: AppTheme.captionSize,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  void _removeFavorite(String id, FavoritesProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إزالة من المفضلة'),
        content: const Text('هل تريد إزالة هذا العنصر من المفضلة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              provider.removeFavorite(id);
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إزالة العنصر من المفضلة'),
                  duration: Duration(seconds: 2),
                ),
              );
            },
            child: const Text('إزالة'),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action, FavoritesProvider provider) {
    switch (action) {
      case 'clear_all':
        _clearAllFavorites(provider);
        break;
      case 'export':
        _exportFavorites(provider);
        break;
    }
  }

  void _clearAllFavorites(FavoritesProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح جميع المفضلة'),
        content: const Text('هل تريد مسح جميع العناصر المفضلة؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              provider.clearAllFavorites();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم مسح جميع المفضلة'),
                  duration: Duration(seconds: 2),
                ),
              );
            },
            child: const Text('مسح الكل'),
          ),
        ],
      ),
    );
  }

  void _exportFavorites(FavoritesProvider provider) {
    final exportText = provider.exportFavoritesAsText();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصدير المفضلة'),
        content: SingleChildScrollView(
          child: Text(exportText),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
