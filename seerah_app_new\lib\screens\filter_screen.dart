import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/seerah_provider.dart';
import '../providers/hadith_provider.dart';

class FilterScreen extends StatefulWidget {
  final String type; // 'seerah' or 'hadith'
  
  const FilterScreen({
    super.key,
    required this.type,
  });

  @override
  State<FilterScreen> createState() => _FilterScreenState();
}

class _FilterScreenState extends State<FilterScreen> {
  String _selectedCategory = 'الكل';
  bool _showOnlyAuthentic = true;

  @override
  void initState() {
    super.initState();
    if (widget.type == 'seerah') {
      final provider = Provider.of<SeerahProvider>(context, listen: false);
      _selectedCategory = provider.selectedCategory;
    } else {
      final provider = Provider.of<HadithProvider>(context, listen: false);
      _selectedCategory = provider.selectedCategory;
      _showOnlyAuthentic = provider.showOnlyAuthentic;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.type == 'seerah' ? 'فلترة السيرة النبوية' : 'فلترة الأحاديث',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: widget.type == 'seerah' 
            ? const Color(0xFF4CAF50) 
            : const Color(0xFFFF9800),
        centerTitle: true,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: _applyFilters,
            child: const Text(
              'تطبيق',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: widget.type == 'seerah'
                ? [
                    const Color(0xFF4CAF50),
                    const Color(0xFF81C784),
                  ]
                : [
                    const Color(0xFFFF9800),
                    const Color(0xFFFFB74D),
                  ],
          ),
        ),
        child: Column(
          children: [
            const SizedBox(height: 20),
            
            // أيقونة القسم
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                widget.type == 'seerah' 
                    ? Icons.filter_list_rounded 
                    : Icons.tune_rounded,
                size: 60,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 20),
            
            // العنوان
            Text(
              widget.type == 'seerah' 
                  ? 'فلترة أحداث السيرة النبوية' 
                  : 'فلترة الأحاديث النبوية',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 30),
            
            // محتوى الفلاتر
            Expanded(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 20),
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // فلتر الفئات
                    const Text(
                      'الفئات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF424242),
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    Expanded(
                      child: widget.type == 'seerah'
                          ? _buildSeerahCategories()
                          : _buildHadithCategories(),
                    ),
                    
                    // فلتر الأحاديث الصحيحة (للأحاديث فقط)
                    if (widget.type == 'hadith') ...[
                      const Divider(height: 40),
                      const Text(
                        'خيارات إضافية',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF424242),
                        ),
                      ),
                      const SizedBox(height: 16),
                      
                      SwitchListTile(
                        title: const Text(
                          'عرض الأحاديث الصحيحة فقط',
                          style: TextStyle(
                            fontSize: 16,
                            color: Color(0xFF424242),
                          ),
                        ),
                        subtitle: const Text(
                          'إخفاء الأحاديث الضعيفة والموضوعة',
                          style: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF757575),
                          ),
                        ),
                        value: _showOnlyAuthentic,
                        onChanged: (value) {
                          setState(() {
                            _showOnlyAuthentic = value;
                          });
                        },
                        activeColor: const Color(0xFFFF9800),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildSeerahCategories() {
    return Consumer<SeerahProvider>(
      builder: (context, provider, child) {
        final categories = provider.categories;
        
        return ListView.builder(
          itemCount: categories.length,
          itemBuilder: (context, index) {
            final category = categories[index];
            final count = category == 'الكل' 
                ? provider.allEvents.length 
                : provider.categoryCounts[category] ?? 0;
            
            return RadioListTile<String>(
              title: Text(
                category,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF424242),
                ),
              ),
              subtitle: Text(
                '$count ${category == 'الكل' ? 'حدث' : 'أحداث'}',
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF757575),
                ),
              ),
              value: category,
              groupValue: _selectedCategory,
              onChanged: (value) {
                setState(() {
                  _selectedCategory = value!;
                });
              },
              activeColor: const Color(0xFF4CAF50),
            );
          },
        );
      },
    );
  }

  Widget _buildHadithCategories() {
    return Consumer<HadithProvider>(
      builder: (context, provider, child) {
        final categories = provider.categories;
        
        return ListView.builder(
          itemCount: categories.length,
          itemBuilder: (context, index) {
            final category = categories[index];
            final count = category == 'الكل' 
                ? provider.allHadiths.length 
                : provider.categoryCounts[category] ?? 0;
            
            return RadioListTile<String>(
              title: Text(
                category,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF424242),
                ),
              ),
              subtitle: Text(
                '$count ${category == 'الكل' ? 'حديث' : 'أحاديث'}',
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF757575),
                ),
              ),
              value: category,
              groupValue: _selectedCategory,
              onChanged: (value) {
                setState(() {
                  _selectedCategory = value!;
                });
              },
              activeColor: const Color(0xFFFF9800),
            );
          },
        );
      },
    );
  }

  void _applyFilters() {
    if (widget.type == 'seerah') {
      final provider = Provider.of<SeerahProvider>(context, listen: false);
      provider.setCategory(_selectedCategory);
    } else {
      final provider = Provider.of<HadithProvider>(context, listen: false);
      provider.setCategory(_selectedCategory);
      if (provider.showOnlyAuthentic != _showOnlyAuthentic) {
        provider.toggleAuthenticOnly();
      }
    }
    
    Navigator.of(context).pop();
  }
}
