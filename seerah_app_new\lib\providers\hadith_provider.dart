import 'package:flutter/foundation.dart';
import '../models/hadith.dart';

class HadithProvider with ChangeNotifier {
  List<Hadith> _hadiths = [];
  List<Hadith> _filteredHadiths = [];
  String _selectedCategory = 'الكل';
  bool _isLoading = false;
  String _searchQuery = '';
  bool _showOnlyAuthentic = true;

  // Getters
  List<Hadith> get hadiths => _filteredHadiths;
  List<Hadith> get allHadiths => _hadiths;
  String get selectedCategory => _selectedCategory;
  bool get isLoading => _isLoading;
  String get searchQuery => _searchQuery;
  bool get showOnlyAuthentic => _showOnlyAuthentic;

  // الحصول على الفئات المتاحة
  List<String> get categories {
    final cats = _hadiths.map((h) => h.category).toSet().toList();
    cats.insert(0, 'الكل');
    return cats;
  }

  // الحصول على عدد الأحاديث في كل فئة
  Map<String, int> get categoryCounts {
    final counts = <String, int>{};
    for (final hadith in _hadiths) {
      if (!_showOnlyAuthentic || hadith.isAuthentic) {
        counts[hadith.category] = (counts[hadith.category] ?? 0) + 1;
      }
    }
    return counts;
  }

  // الحصول على المصادر المتاحة
  List<String> get sources {
    return _hadiths.map((h) => h.source).toSet().toList();
  }

  // تحميل البيانات الأولية
  Future<void> loadInitialData() async {
    _isLoading = true;
    notifyListeners();

    try {
      // محاكاة تحميل البيانات
      await Future.delayed(const Duration(milliseconds: 500));

      _hadiths = _getSampleHadiths();
      _applyFilters();

    } catch (e) {
      debugPrint('خطأ في تحميل بيانات الأحاديث: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // تطبيق الفلاتر
  void _applyFilters() {
    _filteredHadiths = _hadiths.where((hadith) {
      final matchesCategory = _selectedCategory == 'الكل' ||
                             hadith.category == _selectedCategory;
      final matchesSearch = _searchQuery.isEmpty ||
                           hadith.arabicText.contains(_searchQuery) ||
                           hadith.translation.contains(_searchQuery) ||
                           hadith.theme.contains(_searchQuery) ||
                           hadith.keywords.any((k) => k.contains(_searchQuery));
      final matchesAuthentic = !_showOnlyAuthentic || hadith.isAuthentic;

      return matchesCategory && matchesSearch && matchesAuthentic && hadith.isAvailable;
    }).toList();

    // ترتيب حسب رقم الحديث
    _filteredHadiths.sort((a, b) => a.number.compareTo(b.number));
  }

  // تغيير الفئة المختارة
  void setCategory(String category) {
    if (_selectedCategory != category) {
      _selectedCategory = category;
      _applyFilters();
      notifyListeners();
    }
  }

  // تحديث البحث
  void updateSearch(String query) {
    if (_searchQuery != query) {
      _searchQuery = query;
      _applyFilters();
      notifyListeners();
    }
  }

  // تبديل عرض الأحاديث الصحيحة فقط
  void toggleAuthenticOnly() {
    _showOnlyAuthentic = !_showOnlyAuthentic;
    _applyFilters();
    notifyListeners();
  }

  // الحصول على حديث بالمعرف
  Hadith? getHadithById(String id) {
    try {
      return _hadiths.firstWhere((hadith) => hadith.id == id);
    } catch (e) {
      return null;
    }
  }

  // إضافة حديث جديد
  void addHadith(Hadith hadith) {
    _hadiths.add(hadith);
    _applyFilters();
    notifyListeners();
  }

  // تحديث حديث
  void updateHadith(Hadith updatedHadith) {
    final index = _hadiths.indexWhere((hadith) => hadith.id == updatedHadith.id);
    if (index != -1) {
      _hadiths[index] = updatedHadith;
      _applyFilters();
      notifyListeners();
    }
  }

  // حذف حديث
  void removeHadith(String id) {
    _hadiths.removeWhere((hadith) => hadith.id == id);
    _applyFilters();
    notifyListeners();
  }

  // بيانات شاملة للأحاديث النبوية
  List<Hadith> _getSampleHadiths() {
    return [
      // أحاديث العقيدة
      const Hadith(
        id: '1',
        arabicText: 'إِنَّمَا الْأَعْمَالُ بِالنِّيَّاتِ، وَإِنَّمَا لِكُلِّ امْرِئٍ مَا نَوَى، فَمَنْ كَانَتْ هِجْرَتُهُ إِلَى اللَّهِ وَرَسُولِهِ فَهِجْرَتُهُ إِلَى اللَّهِ وَرَسُولِهِ، وَمَنْ كَانَتْ هِجْرَتُهُ لِدُنْيَا يُصِيبُهَا أَوْ امْرَأَةٍ يَنْكِحُهَا فَهِجْرَتُهُ إِلَى مَا هَاجَرَ إِلَيْهِ',
        translation: 'إنما الأعمال بالنيات، وإنما لكل امرئ ما نوى، فمن كانت هجرته إلى الله ورسوله فهجرته إلى الله ورسوله، ومن كانت هجرته لدنيا يصيبها أو امرأة ينكحها فهجرته إلى ما هاجر إليه',
        narrator: 'عمر بن الخطاب رضي الله عنه',
        source: 'صحيح البخاري',
        category: 'أحاديث العقيدة',
        theme: 'النية في الأعمال',
        number: 1,
        keywords: ['النية', 'الأعمال', 'القصد', 'الهجرة'],
      ),
      const Hadith(
        id: '2',
        arabicText: 'بُنِيَ الْإِسْلَامُ عَلَى خَمْسٍ: شَهَادَةِ أَنْ لَا إِلَهَ إِلَّا اللَّهُ وَأَنَّ مُحَمَّدًا رَسُولُ اللَّهِ، وَإِقَامِ الصَّلَاةِ، وَإِيتَاءِ الزَّكَاةِ، وَالْحَجِّ، وَصَوْمِ رَمَضَانَ',
        translation: 'بني الإسلام على خمس: شهادة أن لا إله إلا الله وأن محمداً رسول الله، وإقام الصلاة، وإيتاء الزكاة، والحج، وصوم رمضان',
        narrator: 'عبد الله بن عمر رضي الله عنهما',
        source: 'صحيح البخاري',
        category: 'أحاديث العقيدة',
        theme: 'أركان الإسلام',
        number: 2,
        keywords: ['الإسلام', 'الأركان', 'الشهادة', 'الصلاة', 'الزكاة', 'الحج', 'الصوم'],
      ),
      const Hadith(
        id: '3',
        arabicText: 'الْإِيمَانُ أَنْ تُؤْمِنَ بِاللَّهِ وَمَلَائِكَتِهِ وَكُتُبِهِ وَرُسُلِهِ وَالْيَوْمِ الْآخِرِ وَتُؤْمِنَ بِالْقَدَرِ خَيْرِهِ وَشَرِّهِ',
        translation: 'الإيمان أن تؤمن بالله وملائكته وكتبه ورسله واليوم الآخر وتؤمن بالقدر خيره وشره',
        narrator: 'عمر بن الخطاب رضي الله عنه',
        source: 'صحيح مسلم',
        category: 'أحاديث العقيدة',
        theme: 'أركان الإيمان',
        number: 3,
        keywords: ['الإيمان', 'الله', 'الملائكة', 'الكتب', 'الرسل', 'اليوم الآخر', 'القدر'],
      ),
      const Hadith(
        id: '4',
        arabicText: 'مَنْ قَالَ لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ، فِي يَوْمٍ مِائَةَ مَرَّةٍ كَانَتْ لَهُ عَدْلَ عَشْرِ رِقَابٍ',
        translation: 'من قال لا إله إلا الله وحده لا شريك له، له الملك وله الحمد وهو على كل شيء قدير، في يوم مائة مرة كانت له عدل عشر رقاب',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'صحيح البخاري',
        category: 'أحاديث العقيدة',
        theme: 'فضل التهليل',
        number: 4,
        keywords: ['التهليل', 'التوحيد', 'الذكر', 'الأجر'],
      ),
      const Hadith(
        id: '5',
        arabicText: 'كَلِمَتَانِ خَفِيفَتَانِ عَلَى اللِّسَانِ، ثَقِيلَتَانِ فِي الْمِيزَانِ، حَبِيبَتَانِ إِلَى الرَّحْمَنِ: سُبْحَانَ اللَّهِ وَبِحَمْدِهِ، سُبْحَانَ اللَّهِ الْعَظِيمِ',
        translation: 'كلمتان خفيفتان على اللسان، ثقيلتان في الميزان، حبيبتان إلى الرحمن: سبحان الله وبحمده، سبحان الله العظيم',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'صحيح البخاري',
        category: 'أحاديث العقيدة',
        theme: 'فضل التسبيح',
        number: 5,
        keywords: ['التسبيح', 'الذكر', 'الميزان', 'الأجر'],
      ),

      // أحاديث العبادة
      const Hadith(
        id: '6',
        arabicText: 'الصَّلَاةُ عِمَادُ الدِّينِ، فَمَنْ أَقَامَهَا أَقَامَ الدِّينَ، وَمَنْ هَدَمَهَا هَدَمَ الدِّينَ',
        translation: 'الصلاة عماد الدين، فمن أقامها أقام الدين، ومن هدمها هدم الدين',
        narrator: 'عمر بن الخطاب رضي الله عنه',
        source: 'البيهقي',
        category: 'أحاديث العبادة',
        theme: 'أهمية الصلاة',
        number: 6,
        keywords: ['الصلاة', 'عماد الدين', 'العبادة'],
      ),
      const Hadith(
        id: '7',
        arabicText: 'صُومُوا تَصِحُّوا',
        translation: 'صوموا تصحوا',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'الطبراني',
        category: 'أحاديث العبادة',
        theme: 'فوائد الصوم',
        number: 7,
        keywords: ['الصوم', 'الصحة', 'العبادة'],
      ),
      const Hadith(
        id: '8',
        arabicText: 'مَنْ حَجَّ فَلَمْ يَرْفُثْ وَلَمْ يَفْسُقْ رَجَعَ كَيَوْمِ وَلَدَتْهُ أُمُّهُ',
        translation: 'من حج فلم يرفث ولم يفسق رجع كيوم ولدته أمه',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'صحيح البخاري',
        category: 'أحاديث العبادة',
        theme: 'فضل الحج',
        number: 8,
        keywords: ['الحج', 'المغفرة', 'التوبة'],
      ),
      const Hadith(
        id: '9',
        arabicText: 'مَا نَقَصَتْ صَدَقَةٌ مِنْ مَالٍ، وَمَا زَادَ اللَّهُ عَبْدًا بِعَفْوٍ إِلَّا عِزًّا، وَمَا تَوَاضَعَ أَحَدٌ لِلَّهِ إِلَّا رَفَعَهُ اللَّهُ',
        translation: 'ما نقصت صدقة من مال، وما زاد الله عبداً بعفو إلا عزاً، وما تواضع أحد لله إلا رفعه الله',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'صحيح مسلم',
        category: 'أحاديث العبادة',
        theme: 'فضل الصدقة والعفو',
        number: 9,
        keywords: ['الصدقة', 'العفو', 'التواضع', 'البركة'],
      ),
      const Hadith(
        id: '10',
        arabicText: 'اقْرَءُوا الْقُرْآنَ فَإِنَّهُ يَأْتِي يَوْمَ الْقِيَامَةِ شَفِيعًا لِأَصْحَابِهِ',
        translation: 'اقرءوا القرآن فإنه يأتي يوم القيامة شفيعاً لأصحابه',
        narrator: 'أبو أمامة رضي الله عنه',
        source: 'صحيح مسلم',
        category: 'أحاديث العبادة',
        theme: 'فضل قراءة القرآن',
        number: 10,
        keywords: ['القرآن', 'الشفاعة', 'القراءة'],
      ),

      // أحاديث الأخلاق
      const Hadith(
        id: '11',
        arabicText: 'الْمُسْلِمُ مَنْ سَلِمَ الْمُسْلِمُونَ مِنْ لِسَانِهِ وَيَدِهِ، وَالْمُهَاجِرُ مَنْ هَجَرَ مَا نَهَى اللَّهُ عَنْهُ',
        translation: 'المسلم من سلم المسلمون من لسانه ويده، والمهاجر من هجر ما نهى الله عنه',
        narrator: 'عبد الله بن عمرو رضي الله عنهما',
        source: 'صحيح البخاري',
        category: 'أحاديث الأخلاق',
        theme: 'صفات المسلم الحق',
        number: 11,
        keywords: ['المسلم', 'الأذى', 'اللسان', 'اليد', 'الهجرة'],
      ),
      const Hadith(
        id: '12',
        arabicText: 'إِنَّمَا بُعِثْتُ لِأُتَمِّمَ مَكَارِمَ الْأَخْلَاقِ',
        translation: 'إنما بعثت لأتمم مكارم الأخلاق',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'البخاري في الأدب المفرد',
        category: 'أحاديث الأخلاق',
        theme: 'مقصد البعثة النبوية',
        number: 12,
        keywords: ['الأخلاق', 'البعثة', 'مكارم'],
      ),
      const Hadith(
        id: '13',
        arabicText: 'الْبِرُّ حُسْنُ الْخُلُقِ، وَالْإِثْمُ مَا حَاكَ فِي صَدْرِكَ وَكَرِهْتَ أَنْ يَطَّلِعَ عَلَيْهِ النَّاسُ',
        translation: 'البر حسن الخلق، والإثم ما حاك في صدرك وكرهت أن يطلع عليه الناس',
        narrator: 'النواس بن سمعان رضي الله عنه',
        source: 'صحيح مسلم',
        category: 'أحاديث الأخلاق',
        theme: 'تعريف البر والإثم',
        number: 13,
        keywords: ['البر', 'الخلق', 'الإثم', 'الضمير'],
      ),
      const Hadith(
        id: '14',
        arabicText: 'مَنْ كَانَ يُؤْمِنُ بِاللَّهِ وَالْيَوْمِ الْآخِرِ فَلْيَقُلْ خَيْرًا أَوْ لِيَصْمُتْ',
        translation: 'من كان يؤمن بالله واليوم الآخر فليقل خيراً أو ليصمت',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'صحيح البخاري',
        category: 'أحاديث الأخلاق',
        theme: 'آداب الكلام',
        number: 14,
        keywords: ['الكلام', 'الخير', 'الصمت', 'الإيمان'],
      ),
      const Hadith(
        id: '15',
        arabicText: 'لَيْسَ الْمُؤْمِنُ بِالطَّعَّانِ وَلَا اللَّعَّانِ وَلَا الْفَاحِشِ وَلَا الْبَذِيءِ',
        translation: 'ليس المؤمن بالطعان ولا اللعان ولا الفاحش ولا البذيء',
        narrator: 'عبد الله بن مسعود رضي الله عنه',
        source: 'الترمذي',
        category: 'أحاديث الأخلاق',
        theme: 'صفات المؤمن',
        number: 15,
        keywords: ['المؤمن', 'الطعن', 'اللعن', 'الفحش'],
      ),
    ];
  }
}
